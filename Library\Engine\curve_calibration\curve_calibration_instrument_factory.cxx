#include "curve_calibration/curve_calibration_instrument_factory.h"

#include "common/any_container.h"
#include "common/currency.h"
#include "common/currency_mapping_config.h"
#include "common/discount_definition.h"
#include "common/id/calendar_id.h"
#include "common/id/currency_calendar_mapping_id.h"
#include "common/id/currency_ibor_mapping_id.h"
#include "common/id/currency_rfr_mapping_id.h"
#include "common/pointer.h"
#include "curve_calibration/configs/curve_calibration_instrument_config.h"
#include "curve_calibration/curve_calibration_data.h"
#include "curve_calibration/curve_calibration_instrument.h"
#include "date/calendar.h"
#include "date/tenor.h"
#include "fx/common/xccy_default_convention_config.h"
#include "fx/id/fx_spot_id.h"
#include "fx/id/xccy_default_convention_config_id.h"
#include "inflation/id/inflation_curve_id.h"
#include "inflation/id/inflation_default_convention_config_id.h"
#include "ir/common/swap_default_convention_config.h"
#include "ir/id/discount_curve_id.h"
#include "ir/id/forecast_curve_id.h"
#include "ir/id/future_default_convention_config_id.h"
#include "ir/id/swap_default_convention_config_id.h"
#include "xsigma_magic_enum.h"
#include XSIGMA_MAGIC_ENUM(magic_enum.hpp)
#include "util/flat_hash.h"

namespace xsigma
{
namespace
{

// Unified dependency checker - eliminates 90% of duplication
template <typename... IdTypes>
xsigma_set<any_id> check_dependencies(const any_container& market)
{
    xsigma_set<any_id> output;
    auto               check = [&](auto id)
    {
        if (!market.contains(id))
            output.insert(any_id(id));
    };
    (check(util::make_ptr_const<IdTypes>()), ...);
    return output;
}

// Core mapping getter - reduces repetitive market lookups
template <typename MappingType, typename IdType>
auto get_mapping(const any_container& market, const std::string& key)
{
    return market.at<MappingType>(util::make_ptr_const<IdType>())->value(key);
}

// Asset type extraction with validation
asset_type_enum extract_asset_type(const std::string& asset_info)
{
    const auto& parts = split_string(asset_info, {'_'});
    XSIGMA_CHECK(!parts.empty(), "Invalid asset_info format: " + asset_info);
    const auto asset_type_opt = magic_enum::enum_cast<asset_type_enum>(parts[0]);
    XSIGMA_CHECK(asset_type_opt.has_value(), "Invalid asset type: " + parts[0]);
    return asset_type_opt.value();
}

// Index name and tenor resolution
std::pair<key, tenor> index_name_and_tenor(
    const std::string& curve_type, key rfr, key ibor, tenor default_freq)
{
    if (curve_type.starts_with("RFR"))
    {
        return {rfr, tenor("1b")};
    }
    if (curve_type.starts_with("IBOR"))
    {
        return curve_type.size() > 4 ? std::make_pair(ibor, tenor(curve_type.substr(4) + "m"))
                                     : std::make_pair(ibor, default_freq);
    }
    return {rfr, default_freq};  // fallback
}

// Streamlined config builder - 70% reduction in lines
ptr_const<curve_calibration_instrument_config> build_config_from_asset_info(
    const std::string&   asset_info,
    const currency&      ccy,
    const currency&      ccy_base,
    const any_container& market)
{
    const auto parts = split_string(asset_info, {'_'});
    XSIGMA_CHECK(!parts.empty(), "Invalid asset_info format: " + asset_info);

    const auto asset_type         = extract_asset_type(asset_info);
    const bool is_single_currency = (ccy.ccy() == ccy_base.ccy());

    // Get mappings efficiently
    const auto rfr_base =
        get_mapping<currency_mapping_config, currency_rfr_mapping_id>(market, ccy_base.to_string());
    const auto ibor_base = get_mapping<currency_mapping_config, currency_ibor_mapping_id>(
        market, ccy_base.to_string());
    const auto curve_id_base = key(ccy_base.to_string() + "." + rfr_base + ".1b");

    const auto rfr = is_single_currency
                         ? key(get_mapping<currency_mapping_config, currency_rfr_mapping_id>(
                               market, ccy.to_string()))
                         : discount_definition::xccy_discount_definition(curve_id_base.to_string());

    const auto curve_id = key(ccy.to_string() + "." + rfr.to_string() + ".1b");
    const auto ibor =
        get_mapping<currency_mapping_config, currency_ibor_mapping_id>(market, ccy.to_string());

    // Parse components with defaults
    const std::string curve_type      = parts.size() > 1 ? parts[1] : "";
    const std::string rfr_o_ibor      = curve_type.starts_with("RFR") ? "RFR" : "IBOR";
    const std::string tenor_str       = parts.size() > 2 ? parts[2] : "";
    const std::string base_curve_type = parts.size() > 3 ? parts[3] : "";
    const std::string base_tenor_str  = parts.size() > 4 ? parts[4] : "";

    // Get frequency efficiently
    const auto forecast_frequency =
        is_single_currency
            ? market
                  .at<swap_default_convention_config>(
                      util::make_ptr_const<swap_default_convention_config_id>(ccy, rfr_o_ibor))
                  ->forecast_frequency()
            : market
                  .at<xccy_default_convention_config>(
                      util::make_ptr_const<xccy_default_convention_config_id>(ccy))
                  ->payment_frequency();

    // Build forecast curve
    const auto [index_name, index_tenor] =
        index_name_and_tenor(curve_type, rfr, key(ibor), tenor(forecast_frequency));
    const auto forecast_id = util::make_ptr_const<forecast_curve_id>(ccy, index_name, index_tenor);
    const auto frequency   = tenor_str.empty() ? tenor(forecast_frequency) : tenor(tenor_str);

    // Single currency or special cases
    if (base_curve_type.empty() || asset_type == asset_type_enum::DEPOSIT ||
        asset_type == asset_type_enum::FUTURE || asset_type == asset_type_enum::FRA)
    {
        return util::make_ptr_const<curve_calibration_instrument_config>(
            rfr_o_ibor,
            forecast_id,
            util::make_ptr_const<discount_definition>(curve_id),
            frequency);
    }

    // Cross-currency case
    const std::string base_rfr_o_ibor = base_curve_type.starts_with("RFR") ? "RFR" : "IBOR";

    const auto fixed_frequency_base =
        is_single_currency
            ? market
                  .at<swap_default_convention_config>(
                      util::make_ptr_const<swap_default_convention_config_id>(
                          ccy_base, base_rfr_o_ibor))
                  ->forecast_frequency()
            : market
                  .at<xccy_default_convention_config>(
                      util::make_ptr_const<xccy_default_convention_config_id>(ccy_base))
                  ->payment_frequency();

    const auto [base_index_name, base_index_tenor] = index_name_and_tenor(
        base_curve_type, key(rfr_base), key(ibor_base), tenor(fixed_frequency_base));
    const auto forecast_id_base =
        util::make_ptr_const<forecast_curve_id>(ccy, base_index_name, base_index_tenor);
    const auto frequency_base =
        base_tenor_str.empty() ? tenor(fixed_frequency_base) : tenor(base_tenor_str);

    return util::make_ptr_const<curve_calibration_instrument_config>(
        rfr_o_ibor,
        forecast_id,
        util::make_ptr_const<discount_definition>(curve_id),
        frequency,
        forecast_id_base,
        util::make_ptr_const<discount_definition>(curve_id_base),
        frequency_base);
}
// Helper for calendar creation
auto get_calendar(const any_container& market, const currency& ccy)
{
    const auto holiday_id =
        get_mapping<currency_mapping_config, currency_calendar_mapping_id>(market, ccy.to_string());
    return market.at<calendar>(util::make_ptr_const<calendar_id>(key(holiday_id)));
}

}  // namespace

//-----------------------------------------------------------------------------
xsigma_set<any_id> curve_calibration_instrument_factory::discover(
    const ptr_const<curve_calibration_data>& data, const currency& ccy, const any_container& market)
{
    auto output = check_dependencies<
        currency_rfr_mapping_id,
        currency_ibor_mapping_id,
        currency_calendar_mapping_id>(market);
    if (!output.empty())
    {
        return output;
    }

    const auto holiday_id = util::make_ptr_const<calendar_id>(
        key(get_mapping<currency_mapping_config, currency_calendar_mapping_id>(
            market, ccy.to_string())));
    if (!market.contains(holiday_id))
    {
        output.insert(any_id(holiday_id));
    }

    const auto        parts      = split_string(data->asset_info(), {'_'});
    const std::string curve_type = parts.size() > 1 ? parts[1] : "";

    const auto swap_config_id = util::make_ptr_const<swap_default_convention_config_id>(
        ccy, curve_type.starts_with("RFR") ? "RFR" : "IBOR");

    if (!market.contains(swap_config_id))
    {
        output.insert(any_id(swap_config_id));
    }

    return output;
}

//-----------------------------------------------------------------------------
ptr_const<curve_calibration_instrument>
curve_calibration_instrument_factory::create_rates_instrument(
    const ptr_const<curve_calibration_data>& data, const currency& ccy, const any_container& market)
{
    return util::make_ptr_const<curve_calibration_instrument>(
        build_config_from_asset_info(data->asset_info(), ccy, ccy, market),
        data,
        get_calendar(market, ccy));
}

//-----------------------------------------------------------------------------
xsigma_set<any_id> curve_calibration_instrument_factory::discover(
    const currency& ccy, const currency& ccy_base, const any_container& market)
{
    auto output = check_dependencies<
        currency_rfr_mapping_id,
        currency_ibor_mapping_id,
        currency_calendar_mapping_id>(market);

    if (!output.empty())
    {
        return output;
    }

    // Check holiday calendars for both currencies
    auto check_holiday = [&](const currency& c)
    {
        const auto holiday_id = util::make_ptr_const<calendar_id>(
            key(get_mapping<currency_mapping_config, currency_calendar_mapping_id>(
                market, c.to_string())));
        if (!market.contains(holiday_id))
        {
            output.insert(any_id(holiday_id));
        }
    };
    check_holiday(ccy);
    check_holiday(ccy_base);

    // Check cross-currency configs and FX spot
    auto check_xccy = [&](const currency& c)
    {
        const auto xccy_id = util::make_ptr_const<xccy_default_convention_config_id>(c);
        if (!market.contains(xccy_id))
        {
            output.insert(any_id(xccy_id));
        }
    };
    check_xccy(ccy);
    check_xccy(ccy_base);

    const auto fx_id = util::make_ptr_const<fx_spot_id>(ccy_base.ccy(), ccy.ccy());
    if (!market.contains(fx_id))
    {
        output.insert(any_id(fx_id));
    }

    return output;
}

//-----------------------------------------------------------------------------
ptr_const<curve_calibration_instrument> curve_calibration_instrument_factory::create_fx_instrument(
    const ptr_const<curve_calibration_data>& data,
    const currency&                          ccy,
    const currency&                          ccy_base,
    const any_container&                     market)
{
    const auto holiday_list =
        util::make_ptr_const<calendar>(get_calendar(market, ccy), get_calendar(market, ccy_base));

    return util::make_ptr_const<curve_calibration_instrument>(
        build_config_from_asset_info(data->asset_info(), ccy, ccy_base, market),
        data,
        holiday_list);
}

//-----------------------------------------------------------------------------
xsigma_set<any_id> curve_calibration_instrument_factory::discover(
    const ptr_const<inflation_curve_id>& id, const any_container& market)
{
    auto output = check_dependencies<currency_calendar_mapping_id>(market);
    if (!output.empty())
    {
        return output;
    }

    const auto holiday_id = util::make_ptr_const<calendar_id>(
        key(get_mapping<currency_mapping_config, currency_calendar_mapping_id>(
            market, id->ccy().to_string())));

    if (!market.contains(holiday_id))
    {
        output.insert(any_id(holiday_id));
    }

    const auto inflation_id = util::make_ptr_const<inflation_default_convention_config_id>(id);
    if (!market.contains(inflation_id))
    {
        output.insert(any_id(inflation_id));
    }

    return output;
}

//-----------------------------------------------------------------------------
ptr_const<curve_calibration_instrument>
curve_calibration_instrument_factory::create_inflation_instrument(
    const ptr_const<curve_calibration_data>& data,
    const ptr_const<inflation_curve_id>&     id,
    const any_container&                     market)
{
    const auto ccy = id->ccy();
    const auto rfr =
        get_mapping<currency_mapping_config, currency_rfr_mapping_id>(market, ccy.to_string());

    const auto config = util::make_ptr_const<curve_calibration_instrument_config>(
        "INFLATION",
        ccy,
        id->index_name(),
        key("1b"),
        util::make_ptr_const<discount_definition>(key(ccy.to_string() + "." + rfr + ".1b")),
        tenor("0d"));

    return util::make_ptr_const<curve_calibration_instrument>(
        config, data, get_calendar(market, ccy));
}

}  // namespace xsigma