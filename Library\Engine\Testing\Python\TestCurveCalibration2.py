import sys
import os.path
from datetime import date
from xsigmamodules.Core import timerLog
from xsigmamodules.test import Testing
from xsigmamodules.Engine import (
    curveCalibration,
    curveCalibrationDataRatesId,
    curveCalibrationDataInflationId,
    curveCalibrationData,
    curveCalibrationDataArray,
    curveCalibrationConfigId,
    curveCalibrationConfig,
    curveCalibrationDatesConfigId,
    curveCalibrationDatesConfig,
    curveCalibrationInstrumentConfig,
    calibration_grid_enum,
)
from xsigmamodules.Math import interpolation_enum
from xsigmamodules.Market import (
    currencyMappingConfig,
    discountDefinition,
    discountCurveId,
    discountCurve,
    forecastCurveId,
    forecastCurve,
    inflationCurveId,
    inflationCurve,
    swapDefaultConventionConfig,
    swapDefaultConventionConfigId,
    futureDefaultConventionConfig,
    futureDefaultConventionConfigId,
    xccyDefaultConventionConfig,
    xccyDefaultConventionConfigId,
    calendarId,
    currencyCalendarMappingId,
    currencyRfrMappingId,
    currencyIborMappingId,
    fxSpot,
    fxSpotId,
)
from xsigmamodules.Util import (
    currency,
    calendar,
    dayCountConvention,
    day_count_convention_enum,
    yearMonthDay,
    tenor,
    key,
    scheduleParametersBuilder,
    business_day_convention_enum,
    future_type_enum,
)
from xsigmamodules.Market import (
    anyContainer,
    anyId,
    anyObject,
    valuationDatetime,
    valuationDatetimeId,
    discountCurveFlat,
    forecastCurveFlat,
    inflationDefaultConventionConfig,
    inflationDefaultConventionConfigId,
    inflationFixing,
    inflationFixingId,
    inflationSeasonality,
    inflationSeasonalityId,
)

from xsigmamodules.Instrument import (
    tradeInfoData,
    portfolio,
    deposit,
    future,
    irBasisSwap,
    irFly,
    irSpread,
    irSwap,
    irTermDeposit,
)

from xsigmamodules.TestingUtil import TestingDataSerializer
from xsigmamodules.util.misc import xsigmaGetDataRoot, xsigmaGetTempDir
from xsigmamodules.market import market_data


class TestCurveCalibration2(Testing.xsigmaTest):
    def setUp(self):
        self.DAYS_IN_YEAR = 365
        self.XSIGMA_DATA_ROOT = (
            "C:/dev/cursor_code/PRETORIAN/Testing"  # xsigmaGetDataRoot()
        )
        self.fromYear = 2025
        self.valuationDate = yearMonthDay(self.fromYear, 2, 18).to_datetime()

    def CurveCalibrationDatesConfig(self):
        # OIS dates and values
        ois_dates = [
            "19Mar2025",
            "07May2025",
            "18Jun2025",
            "30Jul2025",
            "17Sep2025",
            "29Oct2025",
            "10Dec2025",
            "28Jan2026",
            "18Mar2026",
            "29Apr2026",
            "17Jun2026",
            "29Jul2026",
            "16Sep2026",
            "28Oct2026",
            "09Dec2026",
            "27Jan2027",
            "17Mar2027",
            "28Apr2027",
            "16Jun2027",
            "28Jul2027",
            "15Sep2027",
            "27Oct2027",
            "15Dec2027",
        ]
        ois_values = [0.0] * len(ois_dates)
        return curveCalibrationDatesConfig(ois_dates, ois_values)

    def curveCalibrationInstrumentsMarketRates(self, valuation_date, ccy, market):
        instruments = []
        # 1. Deposit instruments
        cash_tenor = [
            "DEPOSIT_1b",
            "DEPOSIT_1m",
            "DEPOSIT_2m",
            "DEPOSIT_3m",
            "DEPOSIT_6m",
            "DEPOSIT_12m",
        ]
        cash_implied = [4.3077, 4.4294, 4.5083, 4.5878, 4.7241, 4.9393]
        cash_implied = [value / 100.0 for value in cash_implied]
        instruments.append(
            curveCalibrationData("DEPOSIT_RFR", cash_tenor, cash_implied)
        )

        # 2. Future instruments
        fut_tenors = [
            "Mar25",
            "Apr25",
            "May25",
            "Jun25",
            "Jul25",
            "Aug25",
            "Sep25",
            "Oct25",
            "Nov25",
            "Dec25",
            "Jan26",
            "Feb26",
            "Mar26",
            "Apr26",
            "May26",
            "Jun26",
            "Jul26",
            "Aug26",
            "Sep26",
            "Oct26",
            "Nov26",
            "Dec26",
            "Jan27",
            "Feb27",
            "Mar27",
            "Apr27",
            "May27",
            "Jun27",
            "Jul27",
        ]
        fut_tenors = ["FUTURE_" + tenor for tenor in fut_tenors]
        fut_prices = [
            95.4296,
            95.4672,
            95.5208,
            95.5579,
            95.5936,
            95.6411,
            95.6836,
            95.7145,
            95.7495,
            95.7680,
            95.7887,
            95.8024,
            95.8132,
            95.8212,
            95.8295,
            95.8362,
            95.8399,
            95.8443,
            95.8481,
            95.8492,
            95.8492,
            95.8499,
            95.8479,
            95.8461,
            95.8461,
            95.8308,
            95.8493,
            95.8493,
            95.8378,
        ]
        instruments.append(
            curveCalibrationData("FUTURE_RFR_1M", fut_tenors, fut_prices)
        )

        # 3. Swap instruments
        swap_ois_tenors = [
            "1y",
            "2y",
            "3y",
            "4y",
            "5y",
            "6y",
            "7y",
            "8y",
            "9y",
            "10y",
            "11y",
            "12y",
            "15y",
            "20y",
            "25y",
            "30y",
            "35y",
            "40y",
            "50y",
            "60y",
            "70y",
        ]
        swap_ois_tenors = ["IRSWAP_" + tenor for tenor in swap_ois_tenors]
        swap_ois_par = [
            4.2199,
            4.0944,
            4.0508,
            4.0349,
            4.0309,
            4.0365,
            4.0448,
            4.0536,
            4.0633,
            4.0744,
            4.0869,
            4.1002,
            4.1322,
            4.1317,
            4.0663,
            3.9819,
            3.8831,
            3.7869,
            3.6019,
            3.4620,
            3.3604,
        ]
        swap_ois_par = [value / 100.0 for value in swap_ois_par]
        instruments.append(
            curveCalibrationData("IRSWAP_RFR_3M", swap_ois_tenors, swap_ois_par)
        )

        # 4. Basis swap instruments
        basis_swap_ois_tenors = [
            "1y",
            "2y",
            "3y",
            "4y",
            "5y",
            "6y",
            "7y",
            "8y",
            "9y",
            "10y",
            "11y",
            "12y",
            "15y",
            "20y",
            "25y",
            "30y",
            "35y",
            "40y",
            "50y",
            "60y",
            "70y",
        ]
        basis_swap_ois_tenors = [
            "IRBASISSWAP_" + tenor for tenor in basis_swap_ois_tenors
        ]
        basis_swap_ois_par = [
            4.2199,
            4.0944,
            4.0508,
            4.0349,
            4.0309,
            4.0365,
            4.0448,
            4.0536,
            4.0633,
            4.0744,
            4.0869,
            4.1002,
            4.1322,
            4.1317,
            4.0663,
            3.9819,
            3.8831,
            3.7869,
            3.6019,
            3.4620,
            3.3604,
        ]
        basis_swap_ois_par = [value / 10000.0 for value in basis_swap_ois_par]
        instruments.append(
            curveCalibrationData(
                "IRBASISSWAP_RFR_3M_IBOR3M_3M",
                basis_swap_ois_tenors,
                basis_swap_ois_par,
            )
        )

        return curveCalibrationDataArray(valuation_date, instruments)

    def curveCalibrationInstrumentsMarketFX(
        self, valuation_date, ccy, ccy_base, market
    ):
        instruments = []
        ccbs_tenors = ["1y", "2y", "3y", "4y", "5y", "6y", "7y", "8y"]
        ccbs_tenors = ["CROSSCURRENCYBASISSWAP_" + tenor for tenor in ccbs_tenors]
        ccbs_rates = [2.2966, 2.3700, 2.1825, 2.1900, 2.2375, 2.1938, 2.1888, 2.0800]
        ccbs_rates = [value / 10000.0 for value in ccbs_rates]
        instruments.append(
            curveCalibrationData(
                "CROSSCURRENCYBASISSWAP_RFR_3M_RFR_3M", ccbs_tenors, ccbs_rates
            )
        )
        return curveCalibrationDataArray(valuation_date, instruments)

    def curveCalibrationInstrumentsMarketInflation(
        self, valuation_date, ccy, inflation_index, market
    ):
        instruments = []
        inflation_tenors = [
            "1y",
            "2y",
            "3y",
            "4y",
            "5y",
            "6y",
            "7y",
            "8y",
            "9y",
            "10y",
            "12y",
            "15y",
            "20y",
            "25y",
            "30y",
        ]
        inflation_tenors = [
            "INFLATIONZEROCOUPONSWAP_" + tenor for tenor in inflation_tenors
        ]
        inflation_rates = [
            2.2966,
            2.3700,
            2.1825,
            2.1900,
            2.2375,
            2.1938,
            2.1888,
            2.0800,
            2.1588,
            2.2738,
            2.0913,
            2.1578,
            1.9063,
            1.8713,
            1.8812,
        ]
        inflation_rates = [value / 100.0 for value in inflation_rates]
        instruments.append(
            curveCalibrationData(
                "INFLATIONZEROCOUPONSWAP_RFR_3M", inflation_tenors, inflation_rates
            )
        )
        return curveCalibrationDataArray(valuation_date, instruments)

    def calibrate_rates(
        self, marketContainer, id, ccyBase, useBootstraping, useCeres, useAad
    ):
        if id.ccy().ccy() == ccyBase.ccy():
            marketContainer.insert(
                anyId(curveCalibrationDataRatesId(id, ccyBase)),
                anyObject(
                    self.curveCalibrationInstrumentsMarketRates(
                        self.valuationDate, id.ccy(), marketContainer
                    )
                ),
            )
        else:
            marketContainer.insert(
                anyId(fxSpotId(ccyBase.ccy(), id.ccy().ccy())),
                anyObject(fxSpot(self.valuationDate, 1.1)),
            )
            marketContainer.insert(
                anyId(curveCalibrationDataRatesId(id, ccyBase)),
                anyObject(
                    self.curveCalibrationInstrumentsMarketFX(
                        self.valuationDate, id.ccy(), ccyBase, marketContainer
                    )
                ),
            )

        marketContainer.insert(
            anyId(curveCalibrationConfigId(id.ccy(), id.index_name())),
            anyObject(
                curveCalibrationConfig(
                    2.0,
                    0.0001,
                    1.0e-8,
                    1.0e-8,
                    1.0e-8,
                    -3.0,
                    3.0,
                    6.0e-02,
                    0.2,
                    1.0e-8,
                    500,
                    useCeres,
                    useAad,
                    calibration_grid_enum.INSTRUMENT,
                    interpolation_enum.LINEAR,
                    interpolation_enum.CUBIC_SPLINE,
                    useBootstraping,
                )
            ),
        )

        marketContainer.insert(
            anyId(curveCalibrationDatesConfigId(id.ccy())),
            anyObject(self.CurveCalibrationDatesConfig()),
        )

        market_data.discover(marketContainer, [anyId(id)])
        result = forecastCurve.static_cast(marketContainer.get(anyId(id)))
        return result.rate(
            self.valuationDate, yearMonthDay(self.fromYear, 5, 18).to_datetime()
        )

    def calibrate_inflation(
        self, marketContainer, id, useBootstraping, useCeres, useAad
    ):
        marketContainer.insert(
            anyId(inflationDefaultConventionConfigId(id)),
            anyObject(
                inflationDefaultConventionConfig(
                    "1M",
                    business_day_convention_enum.MODIFIED_FOLLOWING,
                    day_count_convention_enum.ACT_360,
                    2,
                    interpolation_enum.LINEAR,
                )
            ),
        )

        marketContainer.insert(
            anyId(inflationFixingId(id)),
            anyObject(inflationFixing(self.valuationDate, [self.valuationDate], [1.0])),
        )

        marketContainer.insert(
            anyId(inflationSeasonalityId(id)),
            anyObject(
                inflationSeasonality(
                    [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
                )
            ),
        )

        marketContainer.insert(
            anyId(curveCalibrationDataInflationId(id, id.ccy())),
            anyObject(
                self.curveCalibrationInstrumentsMarketInflation(
                    self.valuationDate, id.ccy(), id.index_name(), marketContainer
                )
            ),
        )

        marketContainer.insert(
            anyId(curveCalibrationConfigId(id.ccy(), id.index_name())),
            anyObject(
                curveCalibrationConfig(
                    2.0,
                    0.0001,
                    1.0e-8,
                    1.0e-8,
                    1.0e-8,
                    -3.0,
                    3.0,
                    6.0e-02,
                    0.2,
                    1.0e-8,
                    500,
                    useCeres,
                    useAad,
                    calibration_grid_enum.INSTRUMENT,
                    interpolation_enum.LINEAR_EXPONENTIAL,
                    interpolation_enum.CUBIC_SPLINE,
                    useBootstraping,
                )
            ),
        )

        return inflationCurve.static_cast(marketContainer.get(anyId(id)))

    def testVariations(self, useBootstraping, useCeres=True, useAad=True):
        timer = timerLog()
        timer.StartTimer()

        ccyEur = currency("EUR")
        ccyUsd = currency("USD")

        marketContainer = anyContainer()
        marketContainer.insert(
            anyId(valuationDatetimeId()),
            anyObject(valuationDatetime(self.valuationDate)),
        )

        self.calibrate_rates(
            marketContainer,
            forecastCurveId(ccyUsd, "SOFR", "1b"),
            ccyUsd,
            useBootstraping,
            useCeres,
            useAad,
        )

        self.calibrate_rates(
            marketContainer,
            forecastCurveId(ccyEur, "ESTR", "1b"),
            ccyEur,
            useBootstraping,
            useCeres,
            useAad,
        )

        self.calibrate_rates(
            marketContainer,
            forecastCurveId(ccyUsd, "SOFR", "3m"),
            ccyUsd,
            useBootstraping,
            useCeres,
            useAad,
        )

        self.calibrate_rates(
            marketContainer,
            forecastCurveId(
                ccyEur, discountDefinition.xccy_discount_definition("USD.SOFR.1b"), "1b"
            ),
            ccyUsd,
            useBootstraping,
            useCeres,
            useAad,
        )

        self.calibrate_inflation(
            marketContainer,
            inflationCurveId(ccyUsd, "US.CPI"),
            useBootstraping,
            useCeres,
            useAad,
        )

        timer.StopTimer()
        time = timer.GetElapsedTime()
        print("Curve calibration timer: {0}".format(time))

    def testCurveCalibration(self):
        # Run tests with different combinations of parameters
        """self.testVariations(False, False, True)
        self.testVariations(False, True, True)
        self.testVariations(False, False, False)
        self.testVariations(False, True, False)"""
        self.testVariations(True)
        fromYear = 2025
        valuationDate = yearMonthDay(fromYear, 2, 18).to_datetime()
        marketContainer = anyContainer()
        ccyUsd = currency("USD")
        swap_id = swapDefaultConventionConfigId(currency("USD"), "RFR")
        calendar_id = calendarId("NYSE")
        market_data.discover(marketContainer, [anyId(swap_id), anyId(calendar_id)])

        discount = discountCurveId("USD", "USD.SOFR.1b")
        index = forecastCurveId(ccyUsd, "SOFR", "1b")
        cal = calendar.static_cast(marketContainer.get(anyId(calendar_id)))
        config = swapDefaultConventionConfig.static_cast(
            marketContainer.get(anyId(swap_id))
        )
        cash_tenor = [
            "DEPOSIT_1b",
            "DEPOSIT_1m",
            "DEPOSIT_2m",
            "DEPOSIT_3m",
            "DEPOSIT_6m",
            "DEPOSIT_12m",
        ]
        info = tradeInfoData("DEPOSIT_1b")
        start_date = info.start_date_not_adjusted(valuationDate)
        t = deposit(discount, index, start_date, info.period(), cal, config)
        p = portfolio([t])
        params_schedule = (
            scheduleParametersBuilder().effective_date(valuationDate).build()
        )


if __name__ == "__main__":
    Testing.main([(TestCurveCalibration2, "testCurveCalibration")])
