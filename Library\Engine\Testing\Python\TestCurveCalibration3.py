import sys
import os.path

from datetime import date
from xsigmamodules.Core import timerLog
from xsigmamodules.test import Testing
from xsigmamodules.Engine import (
    curveCalibration,
    curveCalibrationDataRatesId,
    curveCalibrationDataInflationId,
    curveCalibrationData,
    curveCalibrationDataArray,
    curveCalibrationConfigId,
    curveCalibrationConfig,
    curveCalibrationDatesConfigId,
    curveCalibrationDatesConfig,
    curveCalibrationInstrumentConfig,
    calibration_grid_enum,
)
from xsigmamodules.Math import interpolation_enum
from xsigmamodules.Market import (
    currencyMappingConfig,
    discountDefinition,
    discountCurveId,
    discountCurve,
    forecastCurveId,
    forecastCurve,
    inflationCurveId,
    inflationCurve,
    swapDefaultConventionConfigId,
    swapDefaultConventionConfig,
    futureDefaultConventionConfig,
    futureDefaultConventionConfigId,
    xccyDefaultConventionConfig,
    xccyDefaultConventionConfigId,
    calendarId,
    currencyCalendarMappingId,
    currencyRfrMappingId,
    currencyIborMappingId,
    fxSpot,
    fxSpotId,
    anyContainer,
    anyId,
    anyObject,
    valuationDatetime,
    valuationDatetimeId,
    discountCurveFlat,
    forecastCurveFlat,
    inflationDefaultConventionConfig,
    inflationDefaultConventionConfigId,
    inflationFixing,
    inflationFixingId,
    inflationSeasonality,
    inflationSeasonalityId,
)

from xsigmamodules.Util import (
    currency,
    calendar,
    dayCountConvention,
    day_count_convention_enum,
    option_output_enum,
    yearMonthDay,
    tenor,
    key,
    business_day_convention_enum,
    future_type_enum,
    scheduleParametersBuilder,
)
from xsigmamodules.Instrument import (
    tradeInfoData,
    portfolio,
    deposit,
    future,
    irBasisSwap,
    irFly,
    irSpread,
    irSwap,
    irTermDeposit,
    fixedLeg,
    floatLeg,
)
from xsigmamodules.TestingUtil import TestingDataSerializer
from xsigmamodules.util.misc import xsigmaGetDataRoot, xsigmaGetTempDir
from xsigmamodules.market import market_data
from xsigmamodules.instrument.rates import IRSwap, IRBasisSwap, IRTermDeposit
# Consolidated market data
SWAP_6M = [
    2.2576,
    2.2627,
    2.2742,
    2.2993,
    2.3227,
    2.3438,
    2.3648,
    2.3870,
    2.4091,
    2.4298,
    2.4483,
    2.4651,
    2.4926,
    2.4874,
    2.4533,
    2.3655,
    2.2808,
    2.2182,
    2.1638,
    2.0558,
    1.9788,
    1.9218,
    1.8795,
    1.8471,
    1.8218,
]
SWAP_TENORS = [
    "1y",
    "2y",
    "3y",
    "4y",
    "5y",
    "6y",
    "7y",
    "8y",
    "9y",
    "10y",
    "11y",
    "12y",
    "15y",
    "17y",
    "20y",
    "25y",
    "30y",
    "35y",
    "40y",
    "50y",
    "60y",
    "70y",
    "80y",
    "90y",
    "100y",
]
VALUATION_DATE = yearMonthDay(2025, 2, 18).to_datetime()

def CurveCalibrationDatesConfig():
    # OIS dates and values
    ois_dates = [
        "19Mar2025",
        "07May2025",
        "18Jun2025",
        "30Jul2025",
        "17Sep2025",
        "29Oct2025",
        "10Dec2025",
        "28Jan2026",
        "18Mar2026",
        "29Apr2026",
        "17Jun2026",
        "29Jul2026",
        "16Sep2026",
        "28Oct2026",
        "09Dec2026",
        "27Jan2027",
        "17Mar2027",
        "28Apr2027",
        "16Jun2027",
        "28Jul2027",
        "15Sep2027",
        "27Oct2027",
        "15Dec2027",
    ]

    # Initialize ois_values with zeros, matching the length of ois_dates
    ois_values = [0.0] * len(ois_dates)

    # Return the curve calibration dates configuration
    return curveCalibrationDatesConfig(ois_dates, ois_values)

def create_market_instruments():
    # Ultra-compressed instrument data tuples: (asset, output_type, tenors, values)
    data = [
        (
            "DEPOSIT_IBOR",
            option_output_enum.PV,
            ["DEPOSIT_1m", "DEPOSIT_3m", "DEPOSIT_6m", "DEPOSIT_12m"],
            [0.02616, 0.0251, 0.02489, 0.02424],
        ),
        (
            "FUTURE_RFR_1M",
            option_output_enum.PV,
            [
                "FUTURE_" + t
                for t in ["Mar25", "Apr25", "May25", "Jun25", "Jul25", "Aug25"]
            ],
            [97.5855, 97.6849, 97.7751, 97.8297, 97.8728, 97.9142],
        ),
        (
            "FUTURE_RFR_3M",
            option_output_enum.PV,
            [
                "FUTURE_" + t
                for t in [
                    "Sep25",
                    "Dec25",
                    "Mar26",
                    "Jun26",
                    "Sep26",
                    "Dec26",
                    "Mar27",
                    "Jun27",
                    "Sep27",
                    "Dec27",
                ]
            ],
            [
                97.9425,
                97.9894,
                97.9829,
                97.9493,
                97.9153,
                97.8800,
                97.8395,
                97.8080,
                97.7835,
                97.7650,
            ],
        ),
        (
            "IRSWAP_RFR_3M",
            option_output_enum.PAR,
            [
                "IRSWAP_" + t
                for t in [
                    "4y",
                    "5y",
                    "6y",
                    "7y",
                    "8y",
                    "9y",
                    "10y",
                    "11y",
                    "12y",
                    "15y",
                    "17y",
                    "20y",
                    "25y",
                    "30y",
                    "35y",
                    "40y",
                    "50y",
                    "60y",
                ]
            ],
            [
                v / 100
                for v in [
                    2.0952,
                    2.1305,
                    2.1627,
                    2.1946,
                    2.2270,
                    2.2597,
                    2.2905,
                    2.3190,
                    2.3452,
                    2.3933,
                    2.4062,
                    2.3886,
                    2.3219,
                    2.2548,
                    2.2090,
                    2.1697,
                    2.0886,
                    2.0266,
                ]
            ],
        ),
        (
            "IRSWAP_IBOR3M_3M",
            option_output_enum.PAR,
            ["IRSWAP_" + t for t in SWAP_TENORS[:24]],
            [
                v / 100
                for v in [
                    3.7770,
                    3.4940,
                    3.2630,
                    3.1030,
                    3.0140,
                    2.9670,
                    2.9430,
                    2.9360,
                    2.9420,
                    2.9570,
                    2.9770,
                    2.9940,
                    3.0180,
                    2.9360,
                    2.8070,
                    2.6910,
                    2.5000,
                    2.5340,
                    2.4160,
                    2.3220,
                    2.3220,
                    2.3220,
                    2.3220,
                    2.3220,
                ]
            ],
        ),
        (
            "IRSWAP_IBOR",
            option_output_enum.PAR,
            ["IRSWAP_" + t for t in SWAP_TENORS],
            [v / 100 for v in SWAP_6M],
        ),
        (
            "IRFLY_IBOR",
            option_output_enum.PAR,
            [
                "IRFLY_" + t
                for t in [
                    "2s3s4s",
                    "2s3s5s",
                    "2s5s10s",
                    "2s10s20s",
                    "3s4s5s",
                    "4s5s6s",
                    "5s6s7s",
                    "5s7s10s",
                    "5s10s15s",
                    "5s10s30s",
                    "6s7s8s",
                    "7s8s9s",
                    "7s10s15s",
                    "8s9s10s",
                    "10s11s12s",
                    "10s12s15s",
                    "10s15s20s",
                    "10s15s30s",
                    "10s20s30s",
                    "15s17s20s",
                    "15s20s25s",
                    "15s20s30s",
                    "15s25s35s",
                    "20s25s30s",
                    "20s30s40s",
                    "25s30s35s",
                    "30s35s40s",
                    "30s40s50s",
                ]
            ],
            [
                v / 10000
                for v in [
                    -1.36,
                    -3.70,
                    -4.71,
                    14.36,
                    0.17,
                    0.23,
                    0.01,
                    -2.29,
                    4.43,
                    25.61,
                    -0.12,
                    0.01,
                    0.22,
                    0.14,
                    0.17,
                    0.78,
                    10.21,
                    27.46,
                    19.60,
                    2.89,
                    4.85,
                    13.32,
                    2.02,
                    -0.31,
                    -5.55,
                    -2.21,
                    -0.82,
                    -0.90,
                ]
            ],
        ),
        (
            "IRSPREAD_IBOR",
            option_output_enum.PAR,
            [
                "IRSPREAD_" + t
                for t in [
                    "1s2s",
                    "2s5s",
                    "2s10s",
                    "2s20s",
                    "3s4s",
                    "3s5s",
                    "5s7s",
                    "5s10s",
                    "5s30s",
                    "10s12s",
                    "10s15s",
                    "10s20s",
                    "10s25s",
                    "10s30s",
                    "12s20s",
                    "15s25s",
                    "15s30s",
                    "20s30s",
                    "30s35s",
                    "30s40s",
                    "30s50s",
                    "40s50s",
                    "50s60s",
                    "50s70s",
                    "50s80s",
                    "50s90s",
                    "50s100s",
                ]
            ],
            [
                v / 10000
                for v in [
                    0.51,
                    6.00,
                    16.71,
                    19.06,
                    2.51,
                    4.85,
                    4.21,
                    10.71,
                    -4.19,
                    3.53,
                    6.28,
                    2.35,
                    -6.43,
                    -14.90,
                    -1.18,
                    -12.71,
                    -21.18,
                    -17.25,
                    -6.26,
                    -11.70,
                    -22.50,
                    -10.80,
                    -7.70,
                    -13.40,
                    -17.63,
                    -20.87,
                    -23.40,
                ]
            ],
        ),
    ]

    # Basis swap data - shared tenors/values
    basis_tenors = [
        "3M",
        "6M",
        "9M",
        "1Y",
        "18M",
        "2Y",
        "3Y",
        "4Y",
        "5Y",
        "6Y",
        "7Y",
        "8Y",
        "9Y",
        "10Y",
        "12Y",
        "15Y",
        "20Y",
        "25Y",
        "30Y",
        "40Y",
        "50Y",
        "60Y",
        "1y_1y",
        "2y_1y",
        "3y_1y",
        "4y_1y",
        "5y_1y",
        "5y_2y",
    ]
    basis_values = [
        v / 10000
        for v in [
            6.38,
            7.88,
            8.41,
            8.55,
            8.35,
            8.1,
            7.5,
            7,
            6.3,
            5.9,
            4.6,
            3.8,
            3.15,
            2.5,
            1.1,
            -0.6,
            -2,
            -3.6,
            -5,
            -5.8,
            -6.1,
            -6.4,
            7.63,
            6.28,
            5.42,
            3.34,
            1.22,
            0,
        ]
    ]

    basis_3m_12m_tenors = [
        "3M",
        "6M",
        "9M",
        "1Y",
        "18M",
        "2Y",
        "3Y",
        "4Y",
        "5Y",
        "6Y",
        "7Y",
        "8Y",
        "9Y",
        "10Y",
        "15Y",
        "20Y",
        "25Y",
        "30Y",
        "40Y",
        "70Y",
        "3y_1y",
        "4y_1y",
        "5y_1y",
        "5y_2y",
        "6y_1y",
        "7y_1y",
    ]
    basis_3m_12m_values = [
        v / 10000
        for v in [
            8.5,
            9,
            14.03,
            19.95,
            19.97,
            17.85,
            16.65,
            16.35,
            16.2,
            16.45,
            16.05,
            15.8,
            15.55,
            15.6,
            15.75,
            15.25,
            15.05,
            14.2,
            14.4,
            14.79,
            15.4,
            15.57,
            17.79,
            15.64,
            13.45,
            13.89,
        ]
    ]

    data.extend(
        [
            (
                "IRBASISSWAP_IBOR1M_1M_IBOR3M_3M",
                option_output_enum.PAR,
                ["IRBASISSWAP_" + t for t in basis_tenors],
                basis_values,
            ),
            (
                "IRBASISSWAP_IBOR3M_3M_IBOR6M_6M",
                option_output_enum.PAR,
                ["IRBASISSWAP_" + t for t in basis_tenors],
                basis_values,
            ),
            (
                "IRBASISSWAP_IBOR3M_3M_IBOR12M_12M",
                option_output_enum.PAR,
                ["IRBASISSWAP_" + t for t in basis_3m_12m_tenors],
                basis_3m_12m_values,
            ),
        ]
    )

    return curveCalibrationDataArray(
        VALUATION_DATE,
        [
            curveCalibrationData(asset, output, tenors, values)
            for asset, output, tenors, values in data
        ],
    )


def calibrate_rates(mc, id, base_ccy, bootstrap, ceres, aad):
    # Insert market data based on currency match
    data_id = anyId(
        curveCalibrationDataRatesId(id.transform_to_discount_curve_id(), base_ccy)
    )
    mc.insert(data_id,anyObject(create_market_instruments()))

    # Insert calibration config with compressed parameters
    mc.insert(
        anyId(curveCalibrationConfigId(id.ccy(), id.index_name())),
        anyObject(
            curveCalibrationConfig(
                2.0,
                0.0001,
                1e-8,
                1e-8,
                1e-8,
                -3.0,
                3.0,
                0.06,
                0.2,
                1e-8,
                500,
                ceres,
                aad,
                calibration_grid_enum.INSTRUMENT,
                interpolation_enum.LINEAR,
                interpolation_enum.CUBIC_SPLINE,
                bootstrap,
            )
        ),
    )
    mc.insert(
        anyId(curveCalibrationDatesConfigId(id.ccy())),
        anyObject(CurveCalibrationDatesConfig()),
    )

    market_data.discover(mc, [anyId(id)])
    return forecastCurve.static_cast(mc.get(anyId(id)))


def test_variations(bootstrap, ceres=True, aad=True):
    timer = timerLog()
    timer.StartTimer()

    mc = anyContainer()
    mc.insert(
        anyId(valuationDatetimeId()), anyObject(valuationDatetime(VALUATION_DATE))
    )

    # Calibrate EUR curves in compressed loop
    for index, tenor in [("ESTR", "1b"), ("EURIBOR", "6M"), ("EURIBOR", "3M")]:
        calibrate_rates(
            mc, forecastCurveId("EUR", index, tenor), "EUR", bootstrap, ceres, aad
        )

    timer.StopTimer()
    print(f"Curve calibration timer: {timer.GetElapsedTime()}")


def test_curve_calibration():
    [
        test_variations(bootstrap, ceres, aad)
        for bootstrap, ceres, aad in [
            (False, False, False),
            (False, True, False),
            (True, True, True),
        ]
    ]


if __name__ == "__main__":
    test_curve_calibration()
