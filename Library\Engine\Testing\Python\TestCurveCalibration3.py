from datetime import date
from xsigmamodules.Core import timerLog
from xsigmamodules.Engine import *
from xsigmamodules.Math import interpolation_enum
from xsigmamodules.Market import *
from xsigmamodules.Util import *
from xsigmamodules.Instrument import *
from xsigmamodules.TestingUtil import TestingDataSerializer
from xsigmamodules.market import market_data

# Market data - streamlined with butterfly calculations
SWAP_6M_DATA = {
    '1y': 2.2576, '2y': 2.2627, '3y': 2.2742, '4y': 2.2993, '5y': 2.3227, '6y': 2.3438, '7y': 2.3648,
    '8y': 2.3870, '9y': 2.4091, '10y': 2.4298, '11y': 2.4483, '12y': 2.4651, '15y': 2.4926, '17y': 2.4874,
    '20y': 2.4533, '25y': 2.3655, '30y': 2.2808, '35y': 2.2182, '40y': 2.1638, '50y': 2.0558, '60y': 1.9788,
    '70y': 1.9218, '80y': 1.8795, '90y': 1.8471, '100y': 1.8218
}

# Butterfly spreads: asbscs = 2*b - a - c
BUTTERFLY_SPREADS = {
    tenor: 2 * SWAP_6M_DATA[tenor.split('s')[1] + 'y'] - SWAP_6M_DATA[tenor.split('s')[0] + 'y'] - SWAP_6M_DATA[tenor.split('s')[2] + 'y']
    for tenor in ["2s3s4s", "2s3s5s", "2s5s10s", "2s10s20s", "3s4s5s", "4s5s6s", "5s6s7s", "5s7s10s",
                  "5s10s15s", "5s10s30s", "6s7s8s", "7s8s9s", "7s10s15s", "8s9s10s", "10s11s12s",
                  "10s12s15s", "10s15s20s", "10s15s30s", "10s20s30s", "15s17s20s", "15s20s25s",
                  "15s20s30s", "15s25s35s", "20s25s30s", "20s30s40s", "25s30s35s", "30s35s40s", "30s40s50s"]
}

VALUATION_DATE = yearMonthDay(2025, 2, 18).to_datetime()

# Streamlined configuration functions
def create_dates_config():
    dates = ["19Mar2025", "07May2025", "18Jun2025", "30Jul2025", "17Sep2025", "29Oct2025", "10Dec2025",
             "28Jan2026", "18Mar2026", "29Apr2026", "17Jun2026", "29Jul2026", "16Sep2026", "28Oct2026",
             "09Dec2026", "27Jan2027", "17Mar2027", "28Apr2027", "16Jun2027", "28Jul2027", "15Sep2027",
             "27Oct2027", "15Dec2027"]
    return curveCalibrationDatesConfig(dates, [0.0] * len(dates))
def create_market_instruments(valuation_date, ccy, market):
    # Streamlined instrument creation with data compression
    cash_data = ("DEPOSIT_IBOR", ["DEPOSIT_1m", "DEPOSIT_3m", "DEPOSIT_6m", "DEPOSIT_12m"],
                 [v/100 for v in [2.6160, 2.5100, 2.4890, 2.4240]])

    fut_1m = ("FUTURE_RFR_1M", ["FUTURE_" + t for t in ["Mar25", "Apr25", "May25", "Jun25", "Jul25", "Aug25"]],
              [97.5855, 97.6849, 97.7751, 97.8297, 97.8728, 97.9142])

    fut_3m = ("FUTURE_RFR_3M", ["FUTURE_" + t for t in ["Sep25", "Dec25", "Mar26", "Jun26", "Sep26", "Dec26", "Mar27", "Jun27", "Sep27", "Dec27"]],
              [97.9425, 97.9894, 97.9829, 97.9493, 97.9153, 97.8800, 97.8395, 97.8080, 97.7835, 97.7650])

    instruments = [curveCalibrationData(asset, option_output_enum.PV, tenors, values)
                   for asset, tenors, values in [cash_data, fut_1m, fut_3m]]

    # Swap instruments - ultra-compressed with data tuples
    swap_configs = [
        ("IRSWAP_RFR_3M", ["4y","5y","6y","7y","8y","9y","10y","11y","12y","15y","17y","20y","25y","30y","35y","40y","50y","60y"],
         [2.0952,2.1305,2.1627,2.1946,2.2270,2.2597,2.2905,2.3190,2.3452,2.3933,2.4062,2.3886,2.3219,2.2548,2.2090,2.1697,2.0886,2.0266]),
        ("IRSWAP_IBOR3M_3M", list(SWAP_6M_DATA.keys())[:24],
         [3.7770,3.4940,3.2630,3.1030,3.0140,2.9670,2.9430,2.9360,2.9420,2.9570,2.9770,2.9940,3.0180,2.9360,2.8070,2.6910,2.5000,2.5340,2.4160,2.3220,2.3220,2.3220,2.3220,2.3220]),
        ("IRSWAP_IBOR", list(SWAP_6M_DATA.keys()), list(SWAP_6M_DATA.values()))
    ]

    for asset, tenors, values in swap_configs:
        instruments.append(curveCalibrationData(asset, option_output_enum.PAR,
                          ["IRSWAP_" + t for t in tenors], [v/100 for v in values]))
    # 5. Basis swap instruments
    butterfly_spreads = [
    "2s3s4s",
    "2s3s5s",
    "2s5s10s",
    "2s10s20s",
    "3s4s5s",
    "4s5s6s",
    "5s6s7s",
    "5s7s10s",
    "5s10s15s",
    "5s10s30s",
    "6s7s8s",
    "7s8s9s",
    "7s10s15s",
    "8s9s10s",
    "10s11s12s",
    "10s12s15s",
    "10s15s20s",
    "10s15s30s",
    "10s20s30s",
    "15s17s20s",
    "15s20s25s",
    "15s20s30s",
    "15s25s35s",
    "20s25s30s",
    "20s30s40s",
    "25s30s35s",
    "30s35s40s",
    "30s40s50s"
    ]
    butterfly_values =[-1.36, -3.70, -4.71, 14.36, 0.17, 0.23, 0.01, -2.29, 4.43, 25.61, -0.12, 0.01, 0.22, 0.14, 0.17, 0.78, 10.21, 27.46, 19.60, 2.89, 4.85, 13.32, 2.02, -0.31, -5.55, -2.21, -0.82, -0.90]

    fly_tenors = ["IRFLY_" + tenor for tenor in butterfly_spreads]
    fly_par = [value / 10000.0 for value in butterfly_values]

    instruments.append(
        curveCalibrationData(
            "IRFLY_IBOR", option_output_enum.PAR, fly_tenors, fly_par
        )
    )
    # Spread. Basis swap instruments
    spread_tenors = ["1s2s", "2s5s", "2s10s", "2s20s", "3s4s", "3s5s", "5s7s", 
               "5s10s", "5s30s", "10s12s", "10s15s", "10s20s", "10s25s", 
               "10s30s", "12s20s", "15s25s", "15s30s", "20s30s", "30s35s", 
               "30s40s", "30s50s", "40s50s", "50s60s", "50s70s", "50s80s", "50s90s", "50s100s"]
    spread_pars = [0.51, 6.00, 16.71, 19.06, 2.51, 4.85, 4.21, 10.71, -4.19, 3.53, 6.28, 2.35, -6.43, -14.90, -1.18,
     -12.71, -21.18, -17.25, -6.26, -11.70, -22.50, -10.80, -7.70, -13.40, -17.63, -20.87, -23.40]
    
    spread_tenors = ["IRSPREAD_" + tenor for tenor in spread_tenors]
    spread_par = [value / 10000.0 for value in spread_pars]

    instruments.append(
        curveCalibrationData(
            "IRSPREAD_IBOR", option_output_enum.PAR, spread_tenors, spread_par
        )
    )
    
    # 5. Basis swap instruments
    basis_1m_3m_tenors= [
    "3M", "6M", "9M", "1Y", "18M", "2Y", "3Y", "4Y", "5Y", "6Y", 
    "7Y", "8Y", "9Y", "10Y", "12Y", "15Y", "20Y", "25Y", "30Y", 
    "40Y", "50Y", "60Y", "1y_1y", "2y_1y", "3y_1y", "4y_1y", "5y_1y", 
    "5y_2y"
    ]
    basis_1m_3m_pars = [
        6.38, 7.88, 8.41, 8.55, 8.35, 8.1, 7.5, 7, 6.3, 5.9, 
        4.6, 3.8, 3.15, 2.5, 1.1, -0.6, -2, -3.6, -5, -5.8, 
        -6.1, -6.4, 7.63, 6.28, 5.42, 3.34, 1.22, 0
    ]
    basis_1m_3m_pars = [value / 10000.0 for value in basis_1m_3m_pars]
    basis_1m_3m_tenors = ["IRBASISSWAP_" + tenor for tenor in basis_1m_3m_tenors]
    instruments.append(curveCalibrationData("IRBASISSWAP_IBOR1M_1M_IBOR3M_3M", option_output_enum.PAR, basis_1m_3m_tenors, basis_1m_3m_pars))

    basis_3m_6m_tenors= [
    "3M", "6M", "9M", "1Y", "18M", "2Y", "3Y", "4Y", "5Y", "6Y", 
    "7Y", "8Y", "9Y", "10Y", "12Y", "15Y", "20Y", "25Y", "30Y", 
    "40Y", "50Y", "60Y", "1y_1y", "2y_1y", "3y_1y", "4y_1y", "5y_1y", 
    "5y_2y"
    ]
    basis_3m_6m_pars = [
        6.38, 7.88, 8.41, 8.55, 8.35, 8.1, 7.5, 7, 6.3, 5.9, 
        4.6, 3.8, 3.15, 2.5, 1.1, -0.6, -2, -3.6, -5, -5.8, 
        -6.1, -6.4, 7.63, 6.28, 5.42, 3.34, 1.22, 0
    ]
    basis_3m_6m_pars = [value / 10000.0 for value in basis_3m_6m_pars]
    basis_3m_6m_tenors = ["IRBASISSWAP_" + tenor for tenor in basis_3m_6m_tenors]
    instruments.append(curveCalibrationData("IRBASISSWAP_IBOR3M_3M_IBOR6M_6M", option_output_enum.PAR, basis_3m_6m_tenors, basis_3m_6m_pars))

    basis_3m_12m_tenors=["3M", "6M", "9M", "1Y", "18M", "2Y", "3Y", "4Y", "5Y", "6Y", "7Y", 
              "8Y", "9Y", "10Y", "15Y", "20Y", "25Y", "30Y", "40Y", "70Y", 
              "3y_1y", "4y_1y", "5y_1y", "5y_2y", "6y_1y", "7y_1y"]
    basis_3m_12m_pars = [8.5, 9, 14.03, 19.95, 19.97, 17.85, 16.65, 16.35, 16.2, 16.45, 16.05, 
              15.8, 15.55, 15.6, 15.75, 15.25, 15.05, 14.2, 14.4, 14.79, 
              15.4, 15.57, 17.79, 15.64, 13.45, 13.89]
    basis_3m_12m_pars = [value / 10000.0 for value in basis_3m_12m_pars]
    basis_3m_12m_tenors = ["IRBASISSWAP_" + tenor for tenor in basis_3m_12m_tenors]
    instruments.append(curveCalibrationData("IRBASISSWAP_IBOR3M_3M_IBOR12M_12M", option_output_enum.PAR, basis_3m_12m_tenors, basis_3m_12m_pars))

    return curveCalibrationDataArray(valuation_date, instruments)
    
fromYear = 2025
valuationDate = yearMonthDay(fromYear, 2, 18).to_datetime()

def calibrate_rates(marketContainer, id, ccyBase, useBootstraping, useCeres, useAad):
    if id.ccy() == ccyBase:
        marketContainer.insert(
            anyId(
                curveCalibrationDataRatesId(
                    id.transform_to_discount_curve_id(), ccyBase
                )
            ),
            anyObject(
                curveCalibrationInstrumentsMarketRates(
                    valuationDate, id.ccy(), marketContainer
                )
            ),
        )
    else:
        marketContainer.insert(
            anyId(fxSpotId(ccyBase, id.ccy())),
            anyObject(fxSpot(valuationDate, 1.1)),
        )
        marketContainer.insert(
            anyId(
                curveCalibrationDataRatesId(
                    id.transform_to_discount_curve_id(), ccyBase
                )
            ),
            anyObject(
                curveCalibrationInstrumentsMarketFX(
                    valuationDate, id.ccy(), ccyBase, marketContainer
                )
            ),
        )

    marketContainer.insert(
        anyId(curveCalibrationConfigId(id.ccy(), id.index_name())),
        anyObject(
            curveCalibrationConfig(
                2.0,
                0.0001,
                1.0e-8,
                1.0e-8,
                1.0e-8,
                -3.0,
                3.0,
                6.0e-02,
                0.2,
                1.0e-8,
                500,
                useCeres,
                useAad,
                calibration_grid_enum.INSTRUMENT,
                interpolation_enum.LINEAR,
                interpolation_enum.CUBIC_SPLINE,
                useBootstraping,
            )
        ),
    )
    marketContainer.insert(
        anyId(curveCalibrationDatesConfigId(id.ccy())),
        anyObject(CurveCalibrationDatesConfig()),
    )

    market_data.discover(marketContainer, [anyId(id)])
    result = forecastCurve.static_cast(marketContainer.get(anyId(id)))
    
def testVariations(useBootstraping, useCeres=True, useAad=True):
    timer = timerLog()
    timer.StartTimer()

    ccyEur = "EUR"
    ccyUsd = "USD"

    marketContainer = anyContainer()
    marketContainer.insert(
        anyId(valuationDatetimeId()), anyObject(valuationDatetime(valuationDate))
    )
    """calibrate_rates(
        marketContainer,
        forecastCurveId(ccyUsd, "SOFR", "1b"),
        ccyUsd,
        useBootstraping,
        useCeres,
        useAad,
    )"""

    calibrate_rates(
        marketContainer,
        forecastCurveId(ccyEur, "ESTR", "1b"),
        ccyEur,
        useBootstraping,
        useCeres,
        useAad,
    )
    
    calibrate_rates(
        marketContainer,
        forecastCurveId(ccyEur, "EURIBOR", "3M"),
        ccyEur,
        useBootstraping,
        useCeres,
        useAad,
    )
    
    calibrate_rates(
        marketContainer,
        forecastCurveId(ccyEur, "EURIBOR", "6M"),
        ccyEur,
        useBootstraping,
        useCeres,
        useAad,
    )
    

    """calibrate_rates(
        marketContainer,
        forecastCurveId(ccyUsd, "SOFR", "3m"),
        ccyUsd,
        useBootstraping,
        useCeres,
        useAad,
    )

    calibrate_rates(
        marketContainer,
        forecastCurveId(
            ccyEur, discountDefinition.xccy_discount_definition("USD.SOFR.1b"), "1b"
        ),
        ccyUsd,
        useBootstraping,
        useCeres,
        useAad,
    )

    calibrate_inflation(
        marketContainer,
        inflationCurveId(ccyUsd, "US.CPI"),
        useBootstraping,
        useCeres,
        useAad,
    )"""
    timer.StopTimer()
    time = timer.GetElapsedTime()
    print("Curve calibrration timer: {0}".format(time))
    
def testCurveCalibration():
    # Run tests with different combinations of parameters
    #testVariations(False, False, True)
    #testVariations(False, True, True)
    testVariations(False, False, False)
    testVariations(False, True, False)
    testVariations(True)
    
testCurveCalibration()