import sys
import os.path

from datetime import date
from xsigmamodules.test import Testing
from xsigmamodules.Engine import (
    curveCalibration,
    curveCalibrationDataRatesId,
    curveCalibrationDataInflationId,
    curveCalibrationDataArray,
    # curveCalibrationInstrument,
    # curveCalibrationInstrumentsMarketId,
    # curveCalibrationInstrumentsMarket,
    curveCalibrationConfigId,
    curveCalibrationConfig,
    curveCalibrationDatesConfigId,
    curveCalibrationDatesConfig,
    curveCalibrationInstrumentConfig,
    calibration_grid_enum,
)
from xsigmamodules.Math import interpolation_enum
from xsigmamodules.Market import (
    currencyMappingConfig,
    discountDefinition,
    discountCurveId,
    discountCurve,
    forecastCurveId,
    forecastCurve,
    inflationCurveId,
    inflationCurve,
    swapDefaultConventionConfig,
    swapDefaultConventionConfigId,
    futureDefaultConventionConfig,
    futureDefaultConventionConfigId,
    xccyDefaultConventionConfig,
    xccyDefaultConventionConfigId,
    calendarId,
    currencyCalendarMappingId,
    currencyRfrMappingId,
    currencyIborMappingId,
    fxSpot,
    fxSpotId,
)

from xsigmamodules.Util import (
    currency,
    calendar,
    dayCountConvention,
    day_count_convention_enum,
    yearMonthDay,
    tenor,
    key,
    business_day_convention_enum,
    future_type_enum,
)
from xsigmamodules.Market import (
    anyContainer,
    anyId,
    anyObject,
    valuationDatetime,
    valuationDatetimeId,
)
from xsigmamodules.Market import (
    discountCurveFlat,
    forecastCurveFlat,
    inflationDefaultConventionConfig,
    inflationDefaultConventionConfigId,
    inflationFixing,
    inflationFixingId,
    inflationSeasonality,
    inflationSeasonalityId,
)
from xsigmamodules.TestingUtil import TestingDataSerializer

DAYS_IN_YEAR = 365
futureConventions = [
    futureDefaultConventionConfig(
        "1M",
        "SOFR",
        business_day_convention_enum.FIRST_BUSINESS_DAY,
        business_day_convention_enum.LAST_BUSINESS_DAY,
        future_type_enum.ARITHMETIC_AVERAGE,
        day_count_convention_enum.ACT_365_25,
        0,
        4.167,
    ),
    futureDefaultConventionConfig(
        "3M",
        "SOFR",
        business_day_convention_enum.UNADJUSTED,
        business_day_convention_enum.PRECEDING,
        future_type_enum.COMPOUNDING,
        day_count_convention_enum.ACT_360,
        -1,
        4.167,
    ),
    futureDefaultConventionConfig(
        "1M",
        "ESTR",
        business_day_convention_enum.FIRST_BUSINESS_DAY,
        business_day_convention_enum.LAST_BUSINESS_DAY,
        future_type_enum.ARITHMETIC_AVERAGE,
        day_count_convention_enum.ACT_365_25,
        0,
        2.500,
    ),
    futureDefaultConventionConfig(
        "3M",
        "ESTR",
        business_day_convention_enum.UNADJUSTED,
        business_day_convention_enum.PRECEDING,
        future_type_enum.COMPOUNDING,
        day_count_convention_enum.ACT_360,
        -1,
        2.500,
    ),
    futureDefaultConventionConfig(
        "1M",
        "SONIA",
        business_day_convention_enum.FIRST_BUSINESS_DAY,
        business_day_convention_enum.LAST_BUSINESS_DAY,
        future_type_enum.ARITHMETIC_AVERAGE,
        day_count_convention_enum.ACT_365_25,
        0,
        2.500,
    ),
    futureDefaultConventionConfig(
        "3M",
        "SONIA",
        business_day_convention_enum.UNADJUSTED,
        business_day_convention_enum.PRECEDING,
        future_type_enum.COMPOUNDING,
        day_count_convention_enum.ACT_365,
        -1,
        2.500,
    ),
]


class TestCurveCalibration(Testing.xsigmaTest):
    def test(self, id, ccyBase, useBootstraping, useCeres, useAad):
        fromYear = 2025
        valuationDate = yearMonthDay(fromYear, 2, 18).to_datetime()
        marketContainer = anyContainer()

        marketContainer.insert(
            anyId(curveCalibrationDatesConfigId(id.ccy())),
            anyObject(TestingDataSerializer.curveCalibrationDatesConfig()),
        )

        marketContainer.insert(
            anyId(valuationDatetimeId()), anyObject(valuationDatetime(valuationDate))
        )

        marketContainer.insert(
            anyId(currencyCalendarMappingId()),
            anyObject(TestingDataSerializer.currencyCalendarMapping()),
        )

        marketContainer.insert(
            anyId(currencyRfrMappingId()),
            anyObject(TestingDataSerializer.currencyRFRMapping()),
        )

        marketContainer.insert(
            anyId(currencyIborMappingId()),
            anyObject(TestingDataSerializer.currencyIBORMapping()),
        )

        xccy = TestingDataSerializer.currencyXccyDefaultConvention(id.ccy())
        xccyBase = TestingDataSerializer.currencyXccyDefaultConvention(ccyBase)

        marketContainer.insert(
            anyId(xccyDefaultConventionConfigId(id.ccy())), anyObject(xccy)
        )
        marketContainer.insert(
            anyId(xccyDefaultConventionConfigId(ccyBase)), anyObject(xccyBase)
        )
        ccy_str = id.ccy()
        index = "IBOR" if ccy_str == "EUR" else "RFR"

        marketContainer.insert(
            anyId(swapDefaultConventionConfigId(id.ccy(), index)),
            anyObject(
                TestingDataSerializer.currencySwapDefaultConvention(ccy_str, index)
            ),
        )

        if id.ccy() == ccyBase:
            marketContainer.insert(
                anyId(
                    curveCalibrationDataRatesId(
                        id.transform_to_discount_curve_id(), ccyBase
                    )
                ),
                anyObject(
                    TestingDataSerializer.curveCalibrationInstrumentsMarketRates(
                        valuationDate, id.ccy()
                    )
                ),
            )
        else:
            marketContainer.insert(
                anyId(
                    curveCalibrationDataRatesId(
                        id.transform_to_discount_curve_id(), ccyBase
                    )
                ),
                anyObject(
                    TestingDataSerializer.curveCalibrationInstrumentsMarketFX(
                        valuationDate, id.ccy(), ccyBase
                    )
                ),
            )

            marketContainer.insert(
                anyId(discountCurveId("USD", "USD.SOFR.1b")),
                anyObject(discountCurveFlat(valuationDate, 0.01, dayCountConvention())),
            )

            marketContainer.insert(
                anyId(forecastCurveId(ccyBase, "SOFR", "1b")),
                anyObject(forecastCurveFlat(valuationDate, 0.01)),
            )

            marketContainer.insert(
                anyId(forecastCurveId(id.ccy(), "ESTR", "1b")),
                anyObject(forecastCurveFlat(valuationDate, 0.01)),
            )

        ccy_base_str = ccyBase
        index_base = "IBOR" if ccy_base_str == "EUR" else "RFR"

        marketContainer.insert(
            anyId(swapDefaultConventionConfigId(ccyBase, index_base)),
            anyObject(
                TestingDataSerializer.currencySwapDefaultConvention(
                    ccy_base_str, index_base
                )
            ),
        )

        rfr = TestingDataSerializer.currencyRFRMapping().value(id.ccy())

        marketContainer.insert(
            anyId(futureDefaultConventionConfigId(id.ccy(), rfr, "1m")),
            anyObject(futureConventions[0]),
        )

        marketContainer.insert(
            anyId(futureDefaultConventionConfigId(id.ccy(), rfr, "3m")),
            anyObject(futureConventions[1]),
        )

        marketContainer.insert(
            anyId(calendarId("NYSE")),
            anyObject(TestingDataSerializer.createCalendar("NYSE")),
        )

        marketContainer.insert(
            anyId(calendarId("EUR")),
            anyObject(TestingDataSerializer.createCalendar("EUR")),
        )

        marketContainer.insert(
            anyId(fxSpotId(ccyBase, id.ccy())), anyObject(fxSpot(valuationDate, 1.1))
        )

        marketContainer.insert(
            anyId(discountCurveId("EUR", "EUR.ESTR.1b")),
            anyObject(discountCurveFlat(valuationDate, 0.01, dayCountConvention())),
        )

        marketContainer.insert(
            anyId(curveCalibrationConfigId(id.ccy(), id.index_name())),
            anyObject(
                curveCalibrationConfig(
                    2.0,
                    0.0001,
                    1.0e-8,
                    1.0e-8,
                    1.0e-8,
                    -3.0,
                    3.0,
                    6.0e-02,
                    0.2,
                    1.0e-8,
                    500,
                    useCeres,
                    useAad,
                    calibration_grid_enum.INSTRUMENT,
                    interpolation_enum.LINEAR,
                    interpolation_enum.CUBIC_SPLINE,
                    useBootstraping,
                )
            ),
        )

        # Get the curve (this triggers calculation)
        result = forecastCurve.static_cast(marketContainer.get(anyId(id)))
        print(result.rate(valuationDate, yearMonthDay(fromYear, 5, 18).to_datetime()))

    def testInflation(self, id, useBootstraping, useCeres, useAad):

        fromYear = 2025
        valuationDate = yearMonthDay(fromYear, 2, 18).to_datetime()

        marketContainer = anyContainer()
        marketContainer.insert(
            anyId(discountCurveId("USD", "USD.SOFR.1b")),
            anyObject(discountCurveFlat(valuationDate, 0.01, dayCountConvention())),
        )

        marketContainer.insert(
            anyId(inflationDefaultConventionConfigId(id)),
            anyObject(
                inflationDefaultConventionConfig(
                    "1M",
                    business_day_convention_enum.MODIFIED_FOLLOWING,
                    day_count_convention_enum.ACT_360,
                    2,
                    interpolation_enum.LINEAR,
                )
            ),
        )

        marketContainer.insert(
            anyId(inflationFixingId(id)),
            anyObject(inflationFixing(valuationDate, [valuationDate], [1.0])),
        )

        marketContainer.insert(
            anyId(inflationSeasonalityId(id)),
            anyObject(
                inflationSeasonality(
                    [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
                )
            ),
        )

        marketContainer.insert(
            anyId(curveCalibrationDataInflationId(id, id.ccy())),
            anyObject(
                TestingDataSerializer.curveCalibrationInstrumentsMarketInflation(
                    valuationDate, id
                )
            ),
        )

        marketContainer.insert(
            anyId(curveCalibrationDatesConfigId(id.ccy())),
            anyObject(TestingDataSerializer.curveCalibrationDatesConfig()),
        )

        marketContainer.insert(
            anyId(valuationDatetimeId()), anyObject(valuationDatetime(valuationDate))
        )

        marketContainer.insert(
            anyId(currencyCalendarMappingId()),
            anyObject(TestingDataSerializer.currencyCalendarMapping()),
        )

        marketContainer.insert(
            anyId(currencyRfrMappingId()),
            anyObject(TestingDataSerializer.currencyRFRMapping()),
        )

        marketContainer.insert(
            anyId(currencyIborMappingId()),
            anyObject(TestingDataSerializer.currencyIBORMapping()),
        )

        ccy_str = id.ccy()
        index = "IBOR" if ccy_str == "EUR" else "RFR"

        marketContainer.insert(
            anyId(swapDefaultConventionConfigId(id.ccy(), index)),
            anyObject(
                TestingDataSerializer.currencySwapDefaultConvention(ccy_str, index)
            ),
        )

        rfr = TestingDataSerializer.currencyRFRMapping().value(id.ccy())

        marketContainer.insert(
            anyId(futureDefaultConventionConfigId(id.ccy(), rfr, "1m")),
            anyObject(futureConventions[0]),
        )

        marketContainer.insert(
            anyId(futureDefaultConventionConfigId(id.ccy(), rfr, "3m")),
            anyObject(futureConventions[1]),
        )

        marketContainer.insert(
            anyId(calendarId("NYSE")),
            anyObject(TestingDataSerializer.createCalendar("NYSE")),
        )

        marketContainer.insert(
            anyId(calendarId("EUR")),
            anyObject(TestingDataSerializer.createCalendar("EUR")),
        )

        marketContainer.insert(
            anyId(curveCalibrationConfigId(id.ccy(), id.index_name())),
            anyObject(
                curveCalibrationConfig(
                    2.0,
                    0.0001,
                    1.0e-8,
                    1.0e-8,
                    1.0e-8,
                    -3.0,
                    3.0,
                    6.0e-02,
                    0.2,
                    1.0e-8,
                    500,
                    useCeres,
                    useAad,
                    calibration_grid_enum.INSTRUMENT,
                    interpolation_enum.LINEAR_EXPONENTIAL,
                    interpolation_enum.CUBIC_SPLINE,
                    useBootstraping,
                )
            ),
        )

        result = inflationCurve.static_cast(marketContainer.get(anyId(id)))

    def testVariations(self, useBootstraping, useCeres, useAad):
        ccyEur = "EUR"
        ccyUsd = "USD"

        self.test(
            forecastCurveId(ccyUsd, "SOFR", "1b"),
            ccyUsd,
            useBootstraping,
            useCeres,
            useAad,
        )

        self.test(
            forecastCurveId(ccyUsd, "SOFR", "3m"),
            ccyUsd,
            useBootstraping,
            useCeres,
            useAad,
        )

        self.testInflation(
            inflationCurveId(ccyUsd, "US.CPI"), useBootstraping, useCeres, useAad
        )

        self.testInflation(
            inflationCurveId(ccyUsd, "US.CPI"), useBootstraping, useCeres, useAad
        )

        self.test(
            forecastCurveId(
                ccyEur, discountDefinition.xccy_discount_definition("USD.SOFR.1b"), "1b"
            ),
            ccyUsd,
            useBootstraping,
            useCeres,
            useAad,
        )

    def testCurveCalibration(self):
        # Run tests with different combinations of parameters
        self.testVariations(False, False, True)
        self.testVariations(False, False, False)
        self.testVariations(False, True, True)
        self.testVariations(False, True, False)

        self.testVariations(True, False, True)
        self.testVariations(True, False, False)
        self.testVariations(True, True, True)
        self.testVariations(True, True, False)


if __name__ == "__main__":
    Testing.main([(TestCurveCalibration, "testCurveCalibration")])
