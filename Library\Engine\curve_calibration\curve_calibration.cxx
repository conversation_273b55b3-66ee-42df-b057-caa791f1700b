#include "curve_calibration/curve_calibration.h"

#include <fmt/core.h>
#include <fmt/ranges.h>

#include <iostream>
#include <sstream>

#include "common/currency.h"
#include "common/currency_mapping_config.h"
#include "common/discount_definition.h"
#include "common/forecast_config.h"
#include "common/id/calendar_id.h"
#include "common/id/currency_calendar_mapping_id.h"
#include "common/id/currency_rfr_mapping_id.h"
#include "common/id/valuation_datetime_id.h"
#include "common/instrument_enum.h"
#include "common/key.h"
#include "common/priceable.h"
#include "common/valuation_datetime.h"
#include "curve_calibration/configs/curve_calibration_config.h"
#include "curve_calibration/configs/curve_calibration_dates_config.h"
#include "curve_calibration/configs/curve_calibration_instrument_config.h"
#include "curve_calibration/configs/id/curve_calibration_config_id.h"
#include "curve_calibration/configs/id/curve_calibration_dates_config_id.h"
#include "curve_calibration/configs/id/curve_calibration_targets_inflation_id.h"
#include "curve_calibration/configs/id/curve_calibration_targets_rates_id.h"
#include "curve_calibration/curve_calibration_dates.h"
#include "curve_calibration/curve_calibration_instrument.h"
#include "curve_calibration/curve_calibration_target_structure.h"
#include "curve_calibration/curve_calibration_targets.h"
#include "date/calendar.h"
#include "date/datetime_helper.h"
#include "date/year_month_day.h"
#include "fx/fx_spot/fx_spot.h"
#include "fx/id/fx_spot_id.h"
#include "inflation/common/inflation_default_convention_config.h"
#include "inflation/common/inflation_seasonality.h"
#include "inflation/fixing/inflation_fixing.h"
#include "inflation/id/inflation_curve_id.h"
#include "inflation/id/inflation_default_convention_config_id.h"
#include "inflation/id/inflation_fixing_id.h"
#include "inflation/id/inflation_seasonality_id.h"
#include "inflation/inflation_curve/inflation_curve.h"
#include "inflation/inflation_priceable.h"
#include "ir/common/swap_default_convention_config.h"
#include "ir/discount_curve/discount_curve.h"
#include "ir/discount_curve/discount_curve_composite.h"
#include "ir/discount_curve/discount_curve_interpolated.h"
#include "ir/forecast_curve/forecast_curve.h"
#include "ir/forecast_curve/forecast_curve_from_df.h"
#include "ir/id/discount_curve_id.h"
#include "ir/id/forecast_config_id.h"
#include "ir/id/forecast_curve_id.h"
#include "ir/id/swap_default_convention_config_id.h"
#include "optimization_algorithm/ceres_solver_algorithms.h"
#include "optimization_algorithm/levenberg_marquardt.h"
#include "optimization_algorithm/root_finding_algorithms.h"
#include "optimization_algorithm/solver_options/solver_options_ceres.h"
#include "optimization_algorithm/solver_options/solver_options_lm.h"
#include "util/exception.h"
#include "util/logger.h"
#include "util/timer_log.h"

//constructors
namespace xsigma
{
namespace
{
currency currency_from_id(const any_id& id)
{
    if (const auto& forecast_id = id.get<forecast_curve_id>())
    {
        return forecast_id->ccy();
    }
    if (const auto& inflation_id = id.get<inflation_curve_id>())
    {
        return inflation_id->ccy();
    }
    if (const auto& discount_id = id.get<discount_curve_id>())
    {
        return discount_id->ccy();
    }

    XSIGMA_THROW("Unsupported ID type");
}

key index_name(const any_id& id)
{
    if (const auto& forecast_id = id.get<forecast_curve_id>())
    {
        return forecast_id->index_name();
    }
    if (const auto& inflation_id = id.get<inflation_curve_id>())
    {
        return inflation_id->index_name();
    }
    if (const auto& discount_id = id.get<discount_curve_id>())
    {
        return discount_id->id()->id();
    }
    XSIGMA_THROW("Unsupported ID type");
}

ptr_const<curve_calibration_targets> calibration_targets(
    const any_id& id, const any_container& market, const currency& ccy_base)
{
    if (const auto& forecast_id = id.get<forecast_curve_id>())
    {
        return market.at<curve_calibration_targets>(
            util::make_ptr_const<curve_calibration_targets_rates_id>(
                forecast_id->transform_to_discount_curve_id(), ccy_base));
    }
    if (const auto& inflation_id = id.get<inflation_curve_id>())
    {
        return market.at<curve_calibration_targets>(
            util::make_ptr_const<curve_calibration_targets_inflation_id>(inflation_id, ccy_base));
    }
    if (const auto& discount_id = id.get<discount_curve_id>())
    {
        return market.at<curve_calibration_targets>(
            util::make_ptr_const<curve_calibration_targets_rates_id>(discount_id, ccy_base));
    }
    XSIGMA_THROW("Unsupported ID type");
}
}  // namespace

// Implementation details namespace for static functions
namespace impl
{
//-----------------------------------------------------------------------------
void add_id(
    xsigma_set<any_id>&  precalibrated_ids,
    xsigma_set<any_id>&  requested_ids,
    const any_id&        id,
    const any_container& market_container)
{
    if (market_container.contains(id))
    {
        if (precalibrated_ids.find(id) == precalibrated_ids.end())
        {
            precalibrated_ids.emplace(id);
        }
    }
    else if (requested_ids.find(id) == requested_ids.end())
    {
        requested_ids.emplace(id);
    }
}

//-----------------------------------------------------------------------------
void process_requested_id(
    std::vector<xsigma_set<any_id>>&            stage_dependencies,
    xsigma_set<any_id>&                         discovred_ids,
    const any_id&                               id,
    const ptr_const<curve_calibration_targets>& targets,
    const any_container&                        market)
{
    xsigma_set<any_id> dependency;
    dependency.insert(id);

    while (targets->build_curves_depndency(dependency, discovred_ids)) {}

    for (auto it = dependency.begin(); it != dependency.end();)
    {
        if (discovred_ids.find(*it) != discovred_ids.end() || market.contains(*it))
        {
            it = dependency.erase(it);
        }
        else
        {
            ++it;
        }
    }

    if (!dependency.empty())
    {
        std::vector<any_id> new_ids(dependency.begin(), dependency.end());

        for (const auto& new_id : new_ids)
        {
            if (new_id.hash() != id.hash())
            {
                process_requested_id(stage_dependencies, discovred_ids, new_id, targets, market);
            }
        }
        discovred_ids.insert(dependency.begin(), dependency.end());
        stage_dependencies.emplace_back(std::move(dependency));
    }
}

//-----------------------------------------------------------------------------
void build_stage_dependencies(
    std::vector<xsigma_set<any_id>>&            stage_dependencies,
    xsigma_set<any_id>&                         discovred_ids,
    const any_id&                               curve_ois_id,
    const xsigma_set<any_id>&                   requested_ids,
    const ptr_const<curve_calibration_targets>& targets,
    const any_container&                        market_container)
{
    //remove forecast rfr from the discovery as it is associated wih discoun curve id
    ptr_const<forecast_curve_id> forecast_ois_id;
    for (const auto& id : requested_ids)
    {
        if (const auto& tmp = id.get<forecast_curve_id>())
        {
            if (any_id(tmp->transform_to_discount_curve_id()).hash() == curve_ois_id.hash())
            {
                discovred_ids.emplace(id);
                forecast_ois_id = tmp;
                break;
            }
        }
    }

    // Build curve dependency
    if (requested_ids.find(curve_ois_id) != requested_ids.end())
    {
        process_requested_id(
            stage_dependencies, discovred_ids, curve_ois_id, targets, market_container);

        xsigma_set<any_id> ois_ids;
        for (const auto& itr : stage_dependencies)
        {
            ois_ids.insert(itr.begin(), itr.end());
        }
        if (forecast_ois_id != nullptr)
        {
            ois_ids.emplace(any_id(forecast_ois_id));
        }

        stage_dependencies.clear();
        stage_dependencies.emplace_back(std::move(ois_ids));

        XSIGMA_CHECK(
            stage_dependencies.size() == 1,
            "all curve linked to OIS should be in the first stage!");
    }

    // Process remaining requested ids
    for (const auto& id : requested_ids)
    {
        if (discovred_ids.find(id) == discovred_ids.end() && !market_container.contains(id))
        {
            process_requested_id(stage_dependencies, discovred_ids, id, targets, market_container);
        }
    }
}

//-----------------------------------------------------------------------------
void update_market(
    xsigma_set<any_id>&                         discovery_ids,
    xsigma_set<any_id>&                         precalibrated_ids,
    xsigma_set<any_id>&                         requested_ids,
    std::vector<xsigma_set<any_id>>&            stage_dependencies,
    const any_id&                               id,
    const currency&                             ccy_base,
    const any_container&                        market,
    const ptr_const<discount_definition>&       discount_def_base,
    const ptr_const<curve_calibration_targets>& targets,
    bool                                        discovery_mode)
{
    auto update_precalibrated_ids = [&](const any_id& new_id)
    {
        if (discovery_mode)
        {
            if (!market.contains(new_id))
            {
                discovery_ids.emplace(new_id);
            }
        }
    };

    // Extract currency from the id
    const auto& ccy = currency_from_id(id);

    // Create RFR ID
    const auto& rfr_mapping =
        market.at<currency_mapping_config>(util::make_ptr_const<currency_rfr_mapping_id>());

    const auto& rfr_id       = util::make_ptr_const<discount_curve_id>(ccy, ccy_base, rfr_mapping);
    const auto& curve_ois_id = any_id(rfr_id);

    bool foreign_currency = false;

    if (ccy_base.ccy() != ccy.ccy())
    {
        const auto& curve_ois_id_base =
            any_id(util::make_ptr_const<discount_curve_id>(ccy_base, discount_def_base));

        const auto& fx_id = any_id(util::make_ptr_const<fx_spot_id>(ccy_base.ccy(), ccy.ccy()));

        foreign_currency = true;
        update_precalibrated_ids(curve_ois_id_base);
        update_precalibrated_ids(fx_id);

        precalibrated_ids.emplace(curve_ois_id_base);
        precalibrated_ids.emplace(fx_id);
    }

    xsigma_set<any_id> all_requested_ids;

    //filter out the ids that are already in the market
    for (const auto& target : targets->calibration_targets())
    {
        if (target.first == id.hash())
        {
            const auto& trades_ids = target.second.aggregated_market_ids();
            all_requested_ids.insert(trades_ids.begin(), trades_ids.end());
        }
        if (const auto& tmp = id.get<forecast_curve_id>())
        {
            if (target.first == tmp->hash_id(rfr_id))
            {
                const auto& trades_ids = target.second.aggregated_market_ids();
                all_requested_ids.insert(trades_ids.begin(), trades_ids.end());
            }
        }
    }

    xsigma_set<any_id> base_cuve_ids;
    if (id.get<inflation_curve_id>() != nullptr)
    {
        for (const auto& curr_id : all_requested_ids)
        {
            if (curr_id.get<inflation_curve_id>() == nullptr)
            {
                update_precalibrated_ids(curr_id);
                precalibrated_ids.emplace(curr_id);
            }
        }
    }
    else
    {
        for (const auto& curr_id : all_requested_ids)
        {
            if (const auto& tmp = curr_id.get<forecast_curve_id>())
            {
                if (foreign_currency)
                {
                    if (tmp->ccy().ccy() == ccy_base.ccy())
                    {
                        update_precalibrated_ids(curr_id);
                        precalibrated_ids.emplace(curr_id);
                    }
                }
                const auto& id2 =
                    targets->base_curve(tmp, key(rfr_mapping->value(tmp->ccy().ccy().to_string())));

                const auto& curve_id =
                    id2 != nullptr ? id2->transform_to_discount_curve_id() : nullptr;

                if (curve_id != nullptr)
                {
                    if (all_requested_ids.find(any_id(curve_id)) == all_requested_ids.end())
                    {
                        base_cuve_ids.emplace(any_id(curve_id));
                        update_precalibrated_ids(any_id(curve_id));
                    }
                }
            }
        }
    }
    //discover all the all_requested_ids:

    for (const auto& curr_id : all_requested_ids)
    {
        add_id(precalibrated_ids, requested_ids, curr_id, market);
    }

    //exclude forecast curve id associated with the OIS curve id:
    xsigma_set<any_id> discovred_ids(precalibrated_ids.begin(), precalibrated_ids.end());

    build_stage_dependencies(
        stage_dependencies, discovred_ids, curve_ois_id, requested_ids, targets, market);

    if (const auto& tmp = id.get<inflation_curve_id>())
    {
        const auto& id1 = util::make_ptr_const<inflation_fixing_id>(tmp);
        const auto& id2 = util::make_ptr_const<inflation_seasonality_id>(tmp);
        if (discovery_mode)
        {
            update_precalibrated_ids(any_id(id1));
            update_precalibrated_ids(any_id(id2));
        }
    }
}

//-----------------------------------------------------------------------------
void discovery_update_market(
    xsigma_set<any_id>&                   discovered_ids,
    const any_id&                         id,
    const currency&                       ccy_base,
    const ptr_const<discount_definition>& discount_def_base,
    const any_container&                  market)
{
    // Extract currency from the id
    //const auto& ccy = currency_from_id(id);

    const auto& targets = calibration_targets(id, market, ccy_base);

    // Use temporary variables for the static update_market method
    xsigma_set<any_id>              precalibrated_ids;
    xsigma_set<any_id>              requested_ids;
    std::vector<xsigma_set<any_id>> stage_dependencies;

    // Call the static update_market method to populate the requested_ids
    update_market(
        discovered_ids,
        precalibrated_ids,
        requested_ids,
        stage_dependencies,
        id,
        ccy_base,
        market,
        discount_def_base,
        targets,
        true);

    for (const auto& itr : stage_dependencies)
    {
        for (const auto t : itr)
        {
            if (const auto& tmp = t.get<forecast_curve_id>())
            {
                const auto& id_tmp = any_id(util::make_ptr_const<xsigma::forecast_config_id>(tmp));
                if (!market.contains(id_tmp))
                {
                    discovered_ids.emplace(id_tmp);
                }
            }
        }
    }
}

//-----------------------------------------------------------------------------
any_container_precomputed update_precomputed_market(
    const any_container&                        market,
    const xsigma_set<any_id>&                   ids,
    const ptr_const<curve_calibration_targets>& targets)
{
    any_container_precomputed market_precomputed(ids.size());

    for (const auto& id : ids)
    {
        XSIGMA_CHECK_DEBUG(market.contains(id), "");
        market.at<market_data>(id)->update_precomputed_container(
            market_precomputed, id, targets->trades_dates());
    }
    return market_precomputed;
}
}  // namespace impl

//-----------------------------------------------------------------------------
curve_calibration::curve_calibration(
    const any_id& id, const currency& ccy_base, any_container& market)
    : id_(id), ccy_base_(ccy_base), market_(market)
{
    XSIGMA_CHECK(
        id_.get<forecast_curve_id>() || id_.get<inflation_curve_id>() ||
            id_.get<discount_curve_id>(),
        "Unsupported ID type");

    //log_progress("Constructor", "Initializing curve calibration with cross currency:");

    initialize();

    //log_progress("Constructor", "Initialization complete");
}

//-----------------------------------------------------------------------------
void curve_calibration::initialize()
{
    valuation_date_ =
        market_.get<valuation_datetime>(any_id(util::make_ptr_const<valuation_datetime_id>()))
            ->valuation_date(),

    base_date_ = valuation_date_;

    const auto& rfr_mapping =
        market_.get<currency_mapping_config>(util::make_ptr_const<currency_rfr_mapping_id>());

    // Extract currency and index_name from the id
    const auto& ccy = currency_from_id(id_);

    rfr_ = key(rfr_mapping->value(ccy.ccy().to_string()));

    config_ = market_.get<curve_calibration_config>(
        util::make_ptr_const<curve_calibration_config_id>(ccy, index_name(id_)));

    targets_ = calibration_targets(id_, market_, ccy_base_);

    const auto& targets_extras_dates = market_.get<curve_calibration_dates_config>(
        any_id(util::make_ptr_const<curve_calibration_dates_config_id>(ccy)));

    stage_dependencies_.reserve(targets_->calibration_targets().size());

    // add all the ids to the market:
    const auto& discount_def_base =
        util::make_ptr_const<discount_definition>(ccy_base_, ccy_base_, rfr_mapping);
    // Create discount definitions using the currency mapping configurations
    rfr_id_ = util::make_ptr_const<discount_curve_id>(ccy, ccy_base_, rfr_mapping);

    xsigma_set<any_id> discovey_ids;
    impl::update_market(
        discovey_ids,
        precalibrated_ids_,
        requested_ids_,
        stage_dependencies_,
        id_,
        ccy_base_,
        market_,
        discount_def_base,
        targets_,
        false);

    // Populate forecast_configs separately
    for (const auto& itr : stage_dependencies_)
    {
        for (const auto t : itr)
        {
            if (const auto& tmp = t.get<forecast_curve_id>())
            {
                forecast_configs_[t.hash()] = market_.get<forecast_config>(
                    util::make_ptr_const<xsigma::forecast_config_id>(tmp));
            }
        }
    }

    // Process dates
    for (const auto& itr : targets_->calibration_targets())
    {
        auto& obj = calibration_dates_[itr.first];

        obj = curve_calibration_dates(
            valuation_date_,
            targets_extras_dates->dates(),
            itr.second.dates(),
            config_->date_mode_);
    }

    //applay the inflation lag
    if (const auto inflation_index = id_.get<inflation_curve_id>())
    {
        const auto& config = market_.get<inflation_default_convention_config>(
            util::make_ptr_const<inflation_default_convention_config_id>(inflation_index));

        auto applay_lag = [&config](const auto& date)
        {
            auto ymd =
                year_month_day(datetime_helper::add_tenor(date, config->observation_lag(), -1));

            if (ymd.day > config->day_of_the_month())
            {
                ++ymd.month;
                ymd.day = config->day_of_the_month();
                ymd.normalize();
            }
            else
            {
                ymd.day = config->day_of_the_month();
            }

            return ymd.to_datetime();
        };

        base_date_ = applay_lag(valuation_date_);

        //applay the lag to the dates
        for (const auto& itr : targets_->calibration_targets())
        {
            auto& obj = calibration_dates_[itr.first];

            auto& dates = obj.node_dates();

            std::vector<datetime> new_dates;
            new_dates.reserve(dates.size());
            std::transform(dates.begin(), dates.end(), std::back_inserter(new_dates), applay_lag);
            dates = new_dates;

            dates.push_back(datetime_helper::add_tenor(dates.back(), tenor(1, tenor_unit::MONTH)));
        }
    }

    // Create dates indexing
    create_dates_indexing();

    // Calculate number of targets
    size_t i = 0;
    num_of_targets_.resize(stage_dependencies_.size());

    for (const auto& itr : stage_dependencies_)
    {
        auto& num_of_targets = num_of_targets_[i++];
        for (const auto t : itr)
        {
            if (calibration_dates_.find(t.hash()) != calibration_dates_.end())
            {
                num_of_targets += calibration_dates_.at(t.hash()).smoothing_index().size();
                num_of_targets += targets_->calibration_targets().at(t.hash()).trades().size();
            }
        }
    }
}

//-----------------------------------------------------------------------------
xsigma_set<any_id> curve_calibration::discover(
    const any_id& id, const currency& ccy_base, const any_container& market)
{
    // Extract currency and index_name from the id
    currency ccy   = currency_from_id(id);
    key      index = index_name(id);

    xsigma_set<any_id> ids;

    const auto& valuation_date_id = any_id(util::make_ptr_const<valuation_datetime_id>());
    if (!market.contains(valuation_date_id))
    {
        ids.insert(valuation_date_id);
    }

    const auto& rfr_mapping_id = any_id(util::make_ptr_const<currency_rfr_mapping_id>());
    if (!market.contains(rfr_mapping_id))
    {
        ids.insert(rfr_mapping_id);
    }

    const auto& config_id = any_id(util::make_ptr_const<curve_calibration_config_id>(ccy, index));
    if (!market.contains(config_id))
    {
        ids.insert(config_id);
    }

    if (const auto& forecast_id = id.get<forecast_curve_id>())
    {
        const auto& target_id =
            any_id(util::make_ptr_const<curve_calibration_targets_rates_id>(forecast_id, ccy_base));
        if (!market.contains(target_id))
        {
            ids.insert(target_id);
        }
    }
    if (const auto& discount_id = id.get<discount_curve_id>())
    {
        const auto& target_id =
            any_id(util::make_ptr_const<curve_calibration_targets_rates_id>(discount_id, ccy_base));
        if (!market.contains(target_id))
        {
            ids.insert(target_id);
        }
    }

    if (const auto& inflation_id = id.get<inflation_curve_id>())
    {
        const auto& target_id = any_id(
            util::make_ptr_const<curve_calibration_targets_inflation_id>(inflation_id, ccy_base));
        if (!market.contains(target_id))
        {
            ids.insert(target_id);
        }
    }

    const auto& target_extras_dates_id =
        any_id(util::make_ptr_const<curve_calibration_dates_config_id>(ccy));
    if (!market.contains(target_extras_dates_id))
    {
        ids.insert(target_extras_dates_id);
    }

    if (!ids.empty())
    {
        return ids;
    }

    // Get the currency mapping configurations
    const auto& rfr_mapping = market.at<currency_mapping_config>(rfr_mapping_id);

    // Create discount definitions
    ptr_const<discount_definition> discount_def_base =
        util::make_ptr_const<discount_definition>(ccy_base, ccy_base, rfr_mapping);

    impl::discovery_update_market(ids, id, ccy_base, discount_def_base, market);

    if (const auto& tmp = id.get<inflation_curve_id>())
    {
        const auto& id3 = any_id(util::make_ptr_const<inflation_default_convention_config_id>(tmp));
        if (!market.contains(id3))
        {
            ids.insert(id3);
        }
    }
    return ids;
}
}  // namespace xsigma

//helper functions
namespace xsigma
{
//-----------------------------------------------------------------------------
ptr_const<discount_curve> curve_calibration::base_discount_curve(const any_id& id) const
{
    ptr_const<forecast_curve_id> base_id;
    if (const auto& forecast_id = id.get<forecast_curve_id>())
    {
        base_id = targets_->base_curve(forecast_id, rfr_);
        if (base_id->hash() == forecast_id->hash())
        {
            return nullptr;
        }
    }
    else if (const auto& curve_id = id.get<discount_curve_id>())
    {
        base_id = targets_->base_curve(curve_id->transform_to_forecast_curve_id(), rfr_);
        if (base_id->hash() == curve_id->transform_to_forecast_curve_id()->hash())
        {
            return nullptr;
        }
    }

    return base_id != nullptr
               ? market_.get<discount_curve>(base_id->transform_to_discount_curve_id())
               : nullptr;
}

//-----------------------------------------------------------------------------
const std::vector<datetime>& curve_calibration::node_dates(const any_id& id) const
{
    return calibration_dates_.at(id.hash()).node_dates();
}

//-----------------------------------------------------------------------------
void curve_calibration::create_dates_indexing()
{
    const auto& payment_dates = targets_->trades_dates().dates();

    // Payment dates indexing

    for (const auto& itr : payment_dates)
    {
        const auto& dates    = itr.second;
        const auto  hash1    = itr.first;
        auto&       output_h = df_dates_indexing_[hash1];

        output_h.dates_.insert(output_h.dates_.end(), dates.begin(), dates.end());
        output_h.dates_offset_.reserve(dates.size());

        size_t i = 0;
        for (const auto& d : dates)
        {
            auto hash2 = any_dates::hash(d);
            output_h.dates_offset_.emplace_back(hash2);
            output_h.inverse_dates_offset_[hash2] = i;
            i++;
        }
    }

    // Forecast dates indexing
    const auto& forecast_dates = targets_->trades_dates().date_pairs();
    for (const auto& itr : forecast_dates)
    {
        const auto hash     = itr.first;
        auto&      output_h = forecast_dates_indexing_[hash];

        auto& r         = output_h.dates_offset_;
        auto& inverse_r = output_h.inverse_dates_offset_;
        auto& vec       = output_h.dates_;

        vec.resize(itr.second.size());
        size_t j = 0;
        for (const auto& d : itr.second)
        {
            const auto& offset = any_dates::hash(d);
            r.emplace_back(offset);
            inverse_r[offset] = j;
            vec[j]            = d;
            ++j;
        }
    }

    // index dates indexing
    const auto& index_dates = targets_->trades_dates().date_tenor_pairs();
    for (const auto& itr : index_dates)
    {
        const auto hash     = itr.first;
        auto&      output_h = index_dates_indexing_[hash];

        auto& r         = output_h.dates_offset_;
        auto& inverse_r = output_h.inverse_dates_offset_;
        auto& vec       = output_h.dates_;

        vec.resize(itr.second.size());
        size_t j = 0;
        for (const auto& d : itr.second)
        {
            const auto& offset = any_dates::hash(d);
            r.emplace_back(offset);
            inverse_r[offset] = j;
            vec[j]            = d;
            ++j;
        }
    }
}

}  // namespace xsigma

//calibration bootstrapping
namespace xsigma
{
//-----------------------------------------------------------------------------
void curve_calibration::calibrate()
{
    //log_progress("Calibrate",fmt::format("Starting calibration using {}",config_->use_bootstarping_ ? "bootstrap" : "optimization"));

    auto market_precomputed =
        impl::update_precomputed_market(market_, precalibrated_ids_, targets_);

    timer_log timer;
    timer.StartTimer();

    if (config_->use_bootstarping_)
    {
        bootstrap_curves(market_precomputed);
        timer.StopTimer();
        log_progress(
            "Calibrate", fmt::format("Bootstrap time: {} seconds", timer.GetElapsedTime()));
    }
    else
    {
        optimize_curves(market_precomputed);
        timer.StopTimer();
        log_progress(
            "Calibrate", fmt::format("Global solver time: {} seconds", timer.GetElapsedTime()));
    }

    //log_progress("Calibrate", "Calibration completed successfully");
}

//-----------------------------------------------------------------------------
void curve_calibration::bootstrap_curves(any_container_precomputed& market_precomputed)
{
    //log_progress("Bootstrap", "Starting bootstrap curves process");

    for (size_t i = 0, size = stage_dependencies_.size(); i < size; ++i)
    {
        //log_progress("Bootstrap", fmt::format("Processing dependency stage {}/{}", i + 1, size));
        bootstrap_single_curve(market_precomputed, i);
    }

    //log_progress("Bootstrap", "Bootstrap curves process completed successfully");
}

//-----------------------------------------------------------------------------
void curve_calibration::bootstrap_single_curve(
    any_container_precomputed& market_precomputed, size_t dependency_index)
{
    const auto& stage_dependency = stage_dependencies_.at(dependency_index);
    auto        asset_type       = targets_->asset_class();

    if (asset_type == asset_class_enum::INFLATION)
    {
        bootstrap_inflation_curve(market_precomputed, stage_dependency);
    }
    else
    {
        bootstrap_rate_curve(market_precomputed, stage_dependency);
    }
}

//-----------------------------------------------------------------------------
void curve_calibration::bootstrap_inflation_curve(
    any_container_precomputed& market_precomputed, const xsigma_set<any_id>& stage_dependency)
{
    double stdev = config_->bootstrap_stdev_;

    const auto& tmp = id_.get<inflation_curve_id>();

    const auto& inflation_fixings =
        market_.get<inflation_fixing>(util::make_ptr_const<inflation_fixing_id>(tmp));
    const auto& seasonality =
        market_.get<inflation_seasonality>(util::make_ptr_const<inflation_seasonality_id>(tmp));

    for (const auto& itr : stage_dependency)
    {
        const auto hash = itr.hash();

        if (targets_->calibration_targets().find(hash) == targets_->calibration_targets().end())
        {
            continue;
        }

        const auto& targets = targets_->calibration_targets().at(hash);
        const auto  size    = calibration_dates_.at(hash).node_dates().size();

        std::vector<datetime> node_dates_i;
        node_dates_i.reserve(size + 1);

        std::vector<double> rates;
        rates.reserve(size + 1);

        const auto& values       = targets.prices();
        const auto& weights      = targets.weights();
        const auto& output_types = targets.output_types();

        size_t i = 1;
        size_t j = 1;

        auto& curve = market_[itr];

        // Calibrate for each trade
        auto previous_maturity = valuation_date_;
        auto maturity          = valuation_date_;

        double root = 0.;
        node_dates_i.emplace_back(base_date_);
        rates.emplace_back(root);

        any_dates trade_dates;

        for (const auto& trade : targets.trades())
        {
            if (trade->maturity() > maturity)
            {
                maturity = trade->maturity();

                trade_dates.clear();
                trade->accumulate_dates(trade_dates);

                const auto& inflation_trade = dynamic_cast<const inflation_priceable*>(trade.get());
                XSIGMA_CHECK(
                    inflation_trade != nullptr,
                    "Failed to create inflation trade: received null pointer");

                const auto& dates_with_lag = trade_dates.date_tenor_pairs(itr);

                node_dates_i.emplace_back(
                    datetime_helper::add_tenor(maturity, inflation_trade->lag(), -1));

                rates.emplace_back(root);

                auto inflation_functor = [&](double x)
                {
                    rates[j] = x;

                    const auto& inf_curve = util::make_ptr_const<inflation_curve>(
                        valuation_date_,
                        base_date_,
                        config_->interpolations_.front(),
                        node_dates_i,
                        rates,
                        seasonality->seasonality(),
                        inflation_fixings);

                    curve = any_object(inf_curve);

                    auto& cpis = market_precomputed[hash];
                    for (const auto& d : dates_with_lag)
                    {
                        const auto tmp = any_dates::hash(d);
                        cpis[tmp]      = inf_curve->cpi(d.first, d.second);
                    }

                    // Calculate error
                    double par =
                        weights[i - 1] *
                        (values[i - 1] - trade->price(market_precomputed, output_types[i - 1]));
                    return par;
                };

                XSIGMA_CHECK(
                    root_finding_algorithms::brent(
                        inflation_functor, root - stdev, root + stdev, root, 0., 1E-12),
                    " curve not calibrated.");

                j++;
                previous_maturity = maturity;
            }
            i++;
        }
    }
}

//-----------------------------------------------------------------------------
void curve_calibration::bootstrap_rate_curve(
    any_container_precomputed& market_precomputed, const xsigma_set<any_id>& stage_dependency)
{
    double stdev = config_->bootstrap_stdev_;

    ptr_const<forecast_curve_id> forecast_ois_id;
    for (const auto& itr : stage_dependency)
    {
        const auto hash = itr.hash();

        if (targets_->calibration_targets().find(hash) == targets_->calibration_targets().end())
        {
            forecast_ois_id = itr.get<forecast_curve_id>();
            continue;
        }
    }

    for (const auto& itr : stage_dependency)
    {
        const auto hash = itr.hash();

        if (targets_->calibration_targets().find(hash) == targets_->calibration_targets().end())
        {
            continue;
        }

        const auto& targets        = targets_->calibration_targets().at(hash);
        const auto& switching_date = calibration_dates_.at(hash).switching_date();
        const auto  size           = calibration_dates_.at(hash).node_dates().size();

        std::vector<datetime> node_dates_i;
        node_dates_i.reserve(size + 1);

        std::vector<double> rates;
        rates.reserve(size + 1);

        const auto& values       = targets.prices();
        const auto& weights      = targets.weights();
        const auto& output_types = targets.output_types();

        size_t i = 1;
        size_t j = 1;

        // Calibrate for each trade
        auto previous_maturity = valuation_date_;
        auto maturity          = valuation_date_;

        double root = 0.;
        node_dates_i.emplace_back(base_date_);
        rates.emplace_back(0.);

        bool calibrate_discount = itr.get<discount_curve_id>() != nullptr;

        const auto& base_curve = base_discount_curve(itr);

        any_dates trade_dates;

        for (const auto& trade : targets.trades())
        {
            if (trade->maturity() > maturity)
            {
                maturity = trade->maturity();

                trade_dates.clear();
                trade->accumulate_dates(trade_dates);

                const auto& curve_ois_id = *trade->discount_id();

                const auto& payment_dates_i  = trade_dates.dates(curve_ois_id);
                const auto& forecast_dates_i = trade_dates.date_pairs();

                node_dates_i.emplace_back(maturity);
                rates.emplace_back(root);

                auto discount_functor = [&](double x)
                {
                    rates[j] = x;

                    const ptr_const<discount_curve>& tmp_interpolated =
                        util::make_ptr_const<discount_curve_interpolated>(
                            valuation_date_,
                            node_dates_i,
                            rates,
                            std::array<datetime, 1>{switching_date},
                            config_->interpolations_);

                    const auto& discount_curve_tmp =
                        base_curve == nullptr ? tmp_interpolated
                                              : util::make_ptr_const<discount_curve_composite>(
                                                    valuation_date_, tmp_interpolated, base_curve);

                    if (itr.get<forecast_curve_id>() != nullptr)
                    {
                        market_[itr] = any_object(util::make_ptr_const<forecast_curve_from_df>(
                            discount_curve_tmp, forecast_configs_[itr.hash()]));
                    }
                    else if (itr.get<discount_curve_id>() != nullptr)
                    {
                        market_[itr] = any_object(discount_curve_tmp);
                        if (forecast_ois_id != nullptr)
                        {
                            market_[any_id(forecast_ois_id)] =
                                any_object(util::make_ptr_const<forecast_curve_from_df>(
                                    discount_curve_tmp,
                                    forecast_configs_.at(forecast_ois_id->hash())));
                        }
                    }

                    if (calibrate_discount)
                    {
                        auto&       dfs = market_precomputed[itr];
                        const auto& crv = market_.get<discount_curve>(itr);
                        for (const auto& d : payment_dates_i)
                        {
                            const auto tmp = any_dates::hash(d);
                            dfs[tmp]       = exp(crv->log_df(valuation_date_, d));
                        }
                    }

                    // Update forecast rates
                    for (const auto& id : forecast_dates_i)
                    {
                        const auto& forecast = market_.at(id.first).get<xsigma::forecast_curve>();

                        auto& frates = market_precomputed[id.first];
                        for (const auto& it : id.second)
                        {
                            const auto tmp = any_dates::hash(it);
                            frates[tmp]    = forecast->rate(it.first, it.second);
                        }
                    }

                    // Calculate error
                    double par =
                        weights[i - 1] *
                        (values[i - 1] - trade->price(market_precomputed, output_types[i - 1]));
                    return par;
                };

                XSIGMA_CHECK(
                    root_finding_algorithms::brent(
                        discount_functor, root - stdev, root + stdev, root, 0., 1E-12),
                    " curve not calibrated.");

                j++;
                previous_maturity = maturity;
            }
            i++;
        }
    }
}
}  // namespace xsigma

//calibration global solver
namespace xsigma
{
//-----------------------------------------------------------------------------
void curve_calibration::optimize_curves(any_container_precomputed& market_precomputed)
{
    //log_progress("Global solver", "Starting global solver curves process");
    market_precomputed = impl::update_precomputed_market(market_, precalibrated_ids_, targets_);

    for (size_t i = 0, size = stage_dependencies_.size(); i < size; ++i)
    {
        //log_progress("Global solver", fmt::format("Processing dependency stage {}/{}", i + 1, size));
        optimize_single_curve(market_precomputed, i);
    }
    //log_progress("Global solver", "Global solver curves process completed successfully");
}

//-----------------------------------------------------------------------------
void curve_calibration::optimize_single_curve(
    any_container_precomputed& market_precomputed, size_t dependency_index)
{
    // Extract currency from the id
    const auto& curve_ois_id = any_id(rfr_id_);

    const auto& stage_dependency = stage_dependencies_.at(dependency_index);

    /*if (stage_dependency.size() == 1)
    {
        log_progress("Global solver", fmt::format("Processing dependency stage {}/{}", 1, 1));
    }*/

    size_t num_parameters = 0;

    xsigma_set<any_id> calibrated_ids;
    xsigma_set<any_id> non_calibrated_ids;

    for (const auto& itr : stage_dependency)
    {
        const auto hash = itr.hash();
        if (calibration_dates_.find(hash) != calibration_dates_.end())
        {
            const auto& tmp = calibration_dates_.at(hash);
            num_parameters += tmp.node_dates().size() - 1;
            calibrated_ids.emplace(itr);
        }
        else
        {
            non_calibrated_ids.emplace(itr);
        }
    }

    XSIGMA_CHECK(
        non_calibrated_ids.size() <= 1,
        "Only the forecast ID associated with the OIS curve ID is not calibrated.");

    const any_id* forecast_ois_id =
        non_calibrated_ids.size() == 1 ? &(*non_calibrated_ids.begin()) : nullptr;

    if (forecast_ois_id != nullptr)
    {
        XSIGMA_CHECK(
            forecast_ois_id->get<forecast_curve_id>() != nullptr,
            "the forecast ID should be associated with the OIS");
    }

    xsigma_map<any_id, ptr_const<discount_curve>> base_curves;
    for (const auto& itr : calibrated_ids)
    {
        base_curves[itr] = base_discount_curve(itr);
    }

    std::vector<double> x_init(num_parameters, 0.0);

    xsigma_map<size_t, std::vector<double>> rates_map;

    for (const auto& itr : calibrated_ids)
    {
        const auto hash = itr.hash();
        rates_map[hash].resize(calibration_dates_.at(hash).node_dates().size());
    }

    auto objective_function = [this,
                               &rates_map,
                               &calibrated_ids,
                               &stage_dependency,
                               &forecast_ois_id,
                               &base_curves,
                               &market_precomputed](const vector<double>& x, vector<double>& y)
    {
        const auto* xvalues = x.begin();

        for (const auto& itr : calibrated_ids)
        {
            const auto  hash = itr.hash();
            const auto& tmp  = calibration_dates_.at(hash);

            auto& rates = rates_map.at(hash);

            const auto& end = tmp.node_dates().end();

            auto t = tmp.node_dates().begin();

            size_t i   = 0;
            rates[i++] = 0.;
            t++;
            for (; t != end; ++t)
            {
                rates[i++] = *xvalues;
                ++xvalues;
            }
            const auto& curve = create_and_add_curve(itr, tmp, base_curves, rates);

            market_[itr] = curve;

            if (forecast_ois_id != nullptr)
            {
                if (auto disc_curve = curve.get<discount_curve>())
                {
                    market_[*forecast_ois_id] =
                        any_object(util::make_ptr_const<forecast_curve_from_df>(
                            disc_curve, forecast_configs_[forecast_ois_id->hash()]));
                }
            }
        }

        for (const auto& itr : stage_dependency)
        {
            if (const auto& curve_ois = market_.get<discount_curve>(itr))
            {
                const auto& dates      = targets_->trades_dates().dates(itr);
                auto&       output     = market_precomputed[itr];
                auto        itr_offset = df_dates_indexing_.at(itr.hash()).dates_offset_.begin();

                for (const auto& d : dates)
                {
                    auto df = exp(curve_ois->log_df(valuation_date_, d));
                    XSIGMA_FINITE_DEBUG(df);
                    output[*itr_offset] = df;
                    ++itr_offset;
                }
            }
            if (const auto& forecast_curve = market_.get<xsigma::forecast_curve>(itr))
            {
                const auto& dates  = targets_->trades_dates().date_pairs(itr);
                auto&       output = market_precomputed[itr];
                auto itr_offset    = forecast_dates_indexing_.at(itr.hash()).dates_offset_.begin();

                for (const auto& d : dates)
                {
                    auto rate = forecast_curve->rate(d.first, d.second);
                    XSIGMA_FINITE_DEBUG(rate);
                    output[*itr_offset] = rate;
                    ++itr_offset;
                }
            }
            if (const auto& curve = market_.get<inflation_curve>(itr))
            {
                const auto& dates      = targets_->trades_dates().date_tenor_pairs(itr);
                auto&       output     = market_precomputed[itr];
                auto        itr_offset = index_dates_indexing_.at(itr.hash()).dates_offset_.begin();

                for (const auto& d : dates)
                {
                    auto value = curve->cpi(d.first, d.second);
                    XSIGMA_FINITE_DEBUG(value);
                    output[*itr_offset] = value;
                    ++itr_offset;
                }
            }
        }

        size_t k = 0;
        for (const auto& itr : calibrated_ids)
        {
            const auto& hash   = itr.hash();
            const auto& target = targets_->calibration_targets().at(hash);

            auto values       = target.prices().begin();
            auto weights      = target.weights().begin();
            auto output_types = target.output_types().begin();

            for (const auto& trade : target.trades())
            {
                y[k++] = *weights * (*values - trade->price(market_precomputed, *output_types));
                ++values;
                ++weights;
                ++output_types;
            }

            const auto& smoothing_index = calibration_dates_.at(hash).smoothing_index();
            for (const auto m : smoothing_index)
            {
                if (m > 0 && m < x.size() - 1)
                {
                    y[k] = config_->smoothing_weight_ * (x[m + 1] + x[m - 1] - 2. * x[m]);
                }
                else if (m == 0)
                {
                    y[k] = config_->smoothing_weight_ * (x[m + 1] - x[m]);
                }
                else
                {
                    y[k] = config_->smoothing_weight_ * (x[m - 1] - x[m]);
                }
                k++;
            }
        }
    };

    // Initialize state parameters for AAD
    size_t state_parameters_size = 0;

    xsigma_map<size_t, size_t> aad_curve_offset;

    for (const auto& itr : calibrated_ids)
    {
        const auto& hash              = itr.hash();
        const auto& rates             = rates_map.at(hash);
        const auto& calibration_dates = calibration_dates_.at(hash);

        const auto& any_obj_curve =
            create_and_add_curve(itr, calibration_dates, base_curves, rates);

        if (const auto& tmp1 = any_obj_curve.get<discount_curve>())
        {
            aad_curve_offset[hash] = state_parameters_size;
            if (itr == curve_ois_id && forecast_ois_id != nullptr)
            {
                aad_curve_offset[forecast_ois_id->hash()] = state_parameters_size;
            }

            state_parameters_size += tmp1->state_parameters_size();
        }
        else
        {
            aad_curve_offset[hash] = state_parameters_size;
            state_parameters_size += any_obj_curve.get<market_data>()->state_parameters_size();
        }
    }

    vector<double> state_parameters_aad(state_parameters_size);

    any_container_precomputed market_precomputed_aad;
    for (const auto& itr : stage_dependency)
    {
        market_precomputed_aad[itr].reserve(targets_->trades_dates().size(itr));
    }
    for (const auto& id : precalibrated_ids_)
    {
        market_precomputed_aad[id].reserve(targets_->trades_dates().size(id));
    }

    // Define AAD evaluation function
    auto eval_aad = [this,
                     &stage_dependency,
                     &aad_curve_offset,
                     &state_parameters_aad,
                     &market_precomputed,
                     &market_precomputed_aad](
                        const ptr_const<priceable>& trade,
                        double                      weight,
                        option_output_enum          output_type,
                        vector<double>&             y_aad)
    {
        market_precomputed_aad = 0.;
        state_parameters_aad   = 0.;

        trade->price_aad(market_precomputed_aad, -weight, market_precomputed, output_type);

        for (const auto& itr : stage_dependency)
        {
            const auto& hash       = itr.hash();
            const auto& values_aad = market_precomputed_aad.at(itr);
            const auto& aad_offset = aad_curve_offset.at(hash);

            auto* state_parameters_aad_tmp = state_parameters_aad.data() + aad_offset;

            if (const auto& idd = itr.get<discount_curve_id>())
            {
                const auto& curve      = market_.get<discount_curve>(idd);
                const auto& offset_itr = df_dates_indexing_.at(hash).inverse_dates_offset_;
                const auto& dates_itr  = df_dates_indexing_.at(hash).dates_;

                for (const auto& itr2 : values_aad)
                {
                    const auto  j = offset_itr.at(itr2.first);
                    const auto& d = dates_itr[j];
                    curve->df_aad(itr2.second, valuation_date_, d, state_parameters_aad_tmp);
                }
            }
            else if (const auto& idf = itr.get<forecast_curve_id>())
            {
                const auto& curve = market_.get<xsigma::forecast_curve>(idf);

                const auto& offset_itr = forecast_dates_indexing_.at(hash).inverse_dates_offset_;
                const auto& dates_itr  = forecast_dates_indexing_.at(hash).dates_;

                for (const auto& itr2 : values_aad)
                {
                    const auto  j = offset_itr.at(itr2.first);
                    const auto& d = dates_itr[j];
                    curve->rate_aad(itr2.second, d.first, d.second, state_parameters_aad_tmp);
                }
            }
            else if (const auto& idi = itr.get<inflation_curve_id>())
            {
                const auto& curve = market_.get<inflation_curve>(idi);

                const auto& offset_itr = index_dates_indexing_.at(hash).inverse_dates_offset_;
                const auto& dates_itr  = index_dates_indexing_.at(hash).dates_;

                for (const auto& itr2 : values_aad)
                {
                    const auto  j = offset_itr.at(itr2.first);
                    const auto& d = dates_itr[j];
                    curve->cpi_aad(itr2.second, d.first, d.second, state_parameters_aad_tmp);
                }
            }
        }

        for (const auto& itr : stage_dependency)
        {
            const auto& aad_offset = aad_curve_offset.at(itr.hash());

            auto* state_parameters_aad_tmp = state_parameters_aad.data() + aad_offset;

            market_.get<market_data>(itr)->finalize_aad(state_parameters_aad_tmp);

            if (const auto& idd = itr.get<discount_curve_id>())
            {
                market_.get<discount_curve>(idd)->finalize_aad(state_parameters_aad_tmp);
            }
            else if (const auto& idf = itr.get<forecast_curve_id>())
            {
                market_.get<xsigma::forecast_curve>(idf)->finalize_aad(state_parameters_aad_tmp);
            }
            else if (const auto& idi = itr.get<inflation_curve_id>())
            {
                market_.get<inflation_curve>(idi)->finalize_aad(state_parameters_aad_tmp);
            }
        }

        for (size_t k = 0, size = y_aad.size(); k < size; ++k)
        {
            y_aad[k] = state_parameters_aad[k + 1];
        }
    };

    // Define objective function gradient for optimization
    auto objective_function_aad =
        [this, &calibrated_ids, &eval_aad](const vector<double>& x, matrix<double>& y_aad)
    {
        size_t k = 0;
        for (const auto& itr : calibrated_ids)
        {
            const auto& hash        = itr.hash();
            const auto& targets     = targets_->calibration_targets().at(hash);
            const auto& weights     = targets.weights();
            const auto& output_type = targets.output_types();
            const auto& trades      = targets.trades();
            const auto  size        = trades.size();

            for (size_t j = 0; j < size; ++j, ++k)
            {
                vector<double> y_k_aad(y_aad[k]);
                eval_aad(trades[j], weights[j], output_type[j], y_k_aad);
            }

            const auto& smoothing_index = calibration_dates_.at(hash).smoothing_index();
            for (const auto m : smoothing_index)
            {
                if (m > 0 && m < x.size() - 1)
                {
                    vector<double> y_k_aad(y_aad[k++]);
                    y_k_aad        = 0.;
                    y_k_aad[m - 1] = config_->smoothing_weight_;
                    y_k_aad[m]     = -2. * config_->smoothing_weight_;
                    y_k_aad[m + 1] = config_->smoothing_weight_;
                }
                else if (m == 0)
                {
                    vector<double> y_k_aad(y_aad[k++]);
                    y_k_aad        = 0.;
                    y_k_aad[m]     = -config_->smoothing_weight_;
                    y_k_aad[m + 1] = config_->smoothing_weight_;
                }
                else
                {
                    vector<double> y_k_aad(y_aad[k++]);
                    y_k_aad        = 0.;
                    y_k_aad[m]     = -config_->smoothing_weight_;
                    y_k_aad[m - 1] = config_->smoothing_weight_;
                }
            }
        }
    };

    // Run optimization using either Ceres or Levenberg-Marquardt
    if (config_->use_ceres_)
    {
        auto options = util::make_ptr_mutable<solver_options_ceres>(
            config_->max_iterations_,
            config_->ceres_function_tolerance_,
            config_->ceres_gradient_tolerance_,
            config_->ceres_parameter_tolerance_);

        std::vector<double> lb(num_parameters, config_->parameter_lower_bound_);
        std::vector<double> ub(num_parameters, config_->parameter_upper_bound_);

        if (config_->use_aad_)
        {
            ceres_solver_algorithms optimiser_ceres(
                num_parameters,
                num_of_targets_.at(dependency_index),
                objective_function,
                objective_function_aad,
                lb,
                ub);

            optimiser_ceres.solve(x_init, *options);
        }
        else
        {
            ceres_solver_algorithms optimiser_ceres(
                num_parameters, num_of_targets_.at(dependency_index), objective_function, lb, ub);

            optimiser_ceres.solve(x_init, *options);
        }
    }
    else
    {
        vector<double> x(x_init);

        solver_options_lm lm_params(config_->max_iterations_);

        lm_params.set_lambda(config_->lm_lambda_);
        lm_params.set_epsilon(config_->lm_epsilon_);
        lm_params.set_function_tolerance(config_->lm_function_tolerance_);

        if (config_->use_aad_)
        {
            levenberg_marquardt optimiser(
                num_parameters,
                num_of_targets_.at(dependency_index),
                objective_function,
                objective_function_aad);

            auto optimisation_results = optimiser.solve(x, lm_params);

            optimisation_results.print();
        }
        else
        {
            lm_params.set_bump(1e-8);
            levenberg_marquardt optimiser(
                num_parameters, num_of_targets_.at(dependency_index), objective_function);

            auto optimisation_results = optimiser.solve(x, lm_params);

            optimisation_results.print();
        }
    }

    vector<double> x(x_init);
    vector<double> y(num_of_targets_.at(dependency_index));

    objective_function(x, y);
}

//-----------------------------------------------------------------------------
any_object curve_calibration::create_and_add_curve(
    const any_id&                                        id,
    const curve_calibration_dates&                       node_dates,
    const xsigma_map<any_id, ptr_const<discount_curve>>& base_curves,
    const std::vector<double>&                           rates)
{
    auto build_discount_curve = [&]()
    {
        const auto&                      base_curve = base_curves.at(id);
        const ptr_const<discount_curve>& curve = util::make_ptr_const<discount_curve_interpolated>(
            valuation_date_,
            node_dates.node_dates(),
            rates,
            std::array<datetime, 1>{node_dates.switching_date()},
            config_->interpolations_);

        return base_curve != nullptr ? util::make_ptr_const<discount_curve_composite>(
                                           valuation_date_, curve, base_curve)
                                     : curve;
    };

    if (id.get<discount_curve_id>() != nullptr)
    {
        return any_object(build_discount_curve());
    }
    if (const auto& forecast_id = id.get<forecast_curve_id>())
    {
        return any_object(util::make_ptr_const<forecast_curve_from_df>(
            build_discount_curve(), forecast_configs_[id.hash()]));
    }
    if (const auto& inflation_id = id.get<inflation_curve_id>())
    {
        const auto& inflation_fixings =
            market_.get<inflation_fixing>(util::make_ptr_const<inflation_fixing_id>(inflation_id));

        const auto& seasonality = market_.get<inflation_seasonality>(
            util::make_ptr_const<inflation_seasonality_id>(inflation_id));

        return any_object(util::make_ptr_const<inflation_curve>(
            valuation_date_,
            base_date_,
            config_->interpolations_.front(),
            node_dates.node_dates(),
            rates,
            seasonality->seasonality(),
            inflation_fixings));
    }

    XSIGMA_THROW("Unknown curve type: ", id.to_string());
}
}  // namespace xsigma

//sanity check
namespace xsigma
{
//-----------------------------------------------------------------------------
void curve_calibration::calibrated_model_instrument_prices(
    any_container_precomputed& market_precomputed, size_t dependency_index) const
{
    const auto&         stage_dependency = stage_dependencies_.at(dependency_index);
    std::vector<double> y(num_of_targets_.at(dependency_index));

    /*log_progress(
        "calibrated model instrument prices",
        fmt::format("for target: {}", std::string(magic_enum::enum_name(targets_->asset_class()))));*/

    // Calculate target values
    size_t k = 0;
    for (const auto& itr : stage_dependency)
    {
        const auto& hash = itr.hash();
        if (targets_->calibration_targets().find(hash) != targets_->calibration_targets().end())
        {
            const auto& target = targets_->calibration_targets().at(hash);

            auto values       = target.prices().begin();
            auto weights      = target.weights().begin();
            auto output_types = target.output_types().begin();
            for (const auto& trade : target.trades())
            {
                const auto diff =
                    *weights * (*values - trade->price(market_precomputed, *output_types));
                if (std::fabs(diff) > config_->lm_function_tolerance_)
                {
                    XSIGMA_LOGF(
                        WARNING,
                        "target has differences %.3e above tolerance %.1e!!!",
                        diff,
                        config_->lm_function_tolerance_);
                }

                y[k++] = diff;
                ++values;
                ++weights;
                ++output_types;
            }
        }
    }
    fmt::print("------------------\n");
    for (const auto& val : y)
    {
        fmt::print("{}\n", val);
    }
}

//-----------------------------------------------------------------------------
std::string curve_calibration::calibration_summary() const
{
    std::stringstream summary;

    summary << "Curve Calibration Summary\n";
    summary << "========================\n";
    summary << "Valuation Date: " << valuation_date_.to_string() << "\n";
    summary << "Base Date: " << base_date_.to_string() << "\n";

    // Extract and display information from the id
    currency ccy;

    summary << "ID: " << id_.to_string() << "\n";

    if (const auto& forecast_id = id_.get<forecast_curve_id>())
    {
        summary << "Currency: " << forecast_id->ccy().to_string() << "\n";
        summary << "Index Name: " << forecast_id->index_name().to_string() << "\n";
        ccy = forecast_id->ccy();
    }
    else if (const auto& inflation_id = id_.get<inflation_curve_id>())
    {
        summary << "Currency: " << inflation_id->ccy().to_string() << "\n";
        summary << "Index Name: " << inflation_id->index_name().to_string() << "\n";
        ccy = inflation_id->ccy();
    }
    else if (const auto& discount_id = id_.get<discount_curve_id>())
    {
        summary << "Currency: " << discount_id->ccy().to_string() << "\n";
        summary << "Index Name: " << discount_id->id()->id().to_string() << "\n";
        summary << "Discount Definition: " << discount_id->id()->to_string() << "\n";
        ccy = discount_id->ccy();
    }
    else
    {
        XSIGMA_CHECK(false, "Unsupported ID type");
    }

    if (ccy.ccy() != ccy_base_.ccy())
    {
        summary << "Currency (base): " << ccy_base_.to_string() << "\n";
    }

    summary << "\nCalibrated Curves:\n";
    summary << "------------------\n";

    for (const auto& id_pair : requested_ids_)
    {
        summary << "ID: " << id_pair.to_string() << "\n ";

        if (calibration_dates_.find(id_pair.hash()) != calibration_dates_.end())
        {
            const auto& dates = calibration_dates_.at(id_pair.hash()).node_dates();
            summary << "  Node Dates: " << dates.size() << " points\n";

            // If there are fewer than 10 dates, show them all
            if (dates.size() <= 10)
            {
                for (const auto& date : dates)
                {
                    summary << "    " << date << "\n";
                }
            }
            else
            {
                // Otherwise show first 3, last 3
                for (size_t i = 0; i < 3; ++i)
                {
                    summary << "    " << dates[i] << "\n";
                }
                summary << "    ...\n";
                for (size_t i = dates.size() - 3; i < dates.size(); ++i)
                {
                    summary << "    " << dates[i] << "\n";
                }
            }
        }
    }

    return summary.str();
}

//-----------------------------------------------------------------------------
void curve_calibration::debug()
{
    xsigma_set<any_id> ids(precalibrated_ids_.begin(), precalibrated_ids_.end());
    ids.insert(requested_ids_.begin(), requested_ids_.end());

    for (const auto& stage_dependency : stage_dependencies_)
    {
        for (const auto& itr : stage_dependency)
        {
            if (ids.find(itr) == ids.end())
            {
                ids.insert(itr);
            }
        }
    }

    auto market_precomputed = impl::update_precomputed_market(market_, ids, targets_);

    for (size_t i = 0, size = stage_dependencies_.size(); i < size; ++i)
    {
        calibrated_model_instrument_prices(market_precomputed, i);
    }
}

//-----------------------------------------------------------------------------
void curve_calibration::log_progress(const std::string& stage, const std::string& message) const
{
    XSIGMA_VLOGF(log_verbosity_, "[CurveCalibration::%s] %s", stage.c_str(), message.c_str());
}

//-----------------------------------------------------------------------------
void curve_calibration::log_results(
    const any_id&                curve_id,
    const std::vector<datetime>& node_dates,
    const std::vector<double>&   rates) const
{
    std::stringstream ss;
    ss << "Curve ID: " << curve_id.to_string() << ", Points: " << node_dates.size();

    // Log the first few and last few dates/rates if there are many
    if (node_dates.size() <= 6)
    {
        // Log all if 6 or fewer
        for (size_t i = 0; i < node_dates.size(); ++i)
        {
            ss << "\n  " << node_dates[i] << ": " << rates[i];
        }
    }
    else
    {
        // Log first 3 and last 3
        for (size_t i = 0; i < 3; ++i)
        {
            ss << "\n  " << node_dates[i] << ": " << rates[i];
        }
        ss << "\n  ...";
        for (size_t i = node_dates.size() - 3; i < node_dates.size(); ++i)
        {
            ss << "\n  " << node_dates[i] << ": " << rates[i];
        }
    }

    XSIGMA_VLOGF(log_verbosity_, "[CurveCalibration::Results] %s", ss.str().c_str());
}
}  // namespace xsigma
